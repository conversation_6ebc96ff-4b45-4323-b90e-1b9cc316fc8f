#!/usr/bin/env python3
"""
High-Performance Options Backtesting Agent - Modular and Maintainable
"""
import asyncio
import logging
import multiprocessing as mp
from datetime import datetime
from typing import Dict

from .backtesting.config import BacktestConfig
from .backtesting.data_loader import load_feature_engineered_data, load_strategies
from .backtesting.engine import HighPerformanceBacktestEngine
from .backtesting.reporting import (
    generate_backtest_report,
    save_results_for_ai_training,
)

logger = logging.getLogger(__name__)


class OptionsBacktestingAgent:
    """
    Orchestrates the options backtesting process.
    """

    def __init__(self, config_path: str = "config/options_backtesting_config.yaml"):
        self.config_path = config_path
        self.config: BacktestConfig = None
        self.engine: HighPerformanceBacktestEngine = None
        self.backtest_results: Dict = {}

    async def initialize(self, **kwargs):
        """Initialize the agent."""
        try:
            await self._load_config()
            self.engine = HighPerformanceBacktestEngine(risk_free_rate=0.06)
            logger.info("Options Backtesting Agent initialized successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize agent: {e}")
            return False

    async def _load_config(self):
        """Load configuration."""
        # Using default config as per instructions
        self.config = BacktestConfig(
            start_date="2025-08-01",
            end_date="2025-08-31",
            initial_capital=100000.0,
        )
        logger.info("Configuration loaded successfully")

    async def start(self, **kwargs) -> bool:
        """Start the backtesting process."""
        try:
            logger.info("Starting Options Backtesting Agent...")
            
            strategies = await load_strategies()
            if not strategies:
                logger.warning("No strategies found for backtesting")
                return False

            feature_data = await load_feature_engineered_data(
                self.config.start_date, self.config.end_date
            )
            if feature_data is None or feature_data.is_empty():
                logger.warning("No feature-engineered data found for the specified date range")
                return False

            self.backtest_results = self.engine.run_backtest(
                feature_data, strategies, self.config
            )

            await generate_backtest_report(self.backtest_results)
            await save_results_for_ai_training(self.backtest_results)

            logger.info("Backtesting completed successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start agent: {e}", exc_info=True)
            return False

    async def cleanup(self):
        """Cleanup resources."""
        logger.info("Cleaning up Options Backtesting Agent...")


async def main():
    """Example usage of Options Backtesting Agent"""
    agent = OptionsBacktestingAgent()
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(main())
