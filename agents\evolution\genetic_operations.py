#!/usr/bin/env python3
"""
Genetic Operations Module for Options Strategy Evolution

This module handles all genetic algorithm operations including selection, crossover, and mutation.
It uses performance-based selection and data-driven parameter optimization.
"""

import asyncio
import logging
import random
import hashlib
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

@dataclass
class MutationConfig:
    """Configuration for mutation operations"""
    rsi_range: Tuple[int, int] = (5, 25)
    ma_range: Tuple[int, int] = (5, 50)
    stop_loss_range: Tuple[float, float] = (0.01, 0.05)
    take_profit_range: Tuple[float, float] = (0.02, 0.10)
    iv_rank_range: Tuple[int, int] = (10, 90)
    timeframe_options: List[str] = None
    
    def __post_init__(self):
        if self.timeframe_options is None:
            self.timeframe_options = ['1min', '3min', '5min', '15min', '30min']

class GeneticOperations:
    """Handles genetic algorithm operations for strategy evolution"""
    
    def __init__(self, performance_analyzer, thread_pool: Optional[ThreadPoolExecutor] = None):
        self.performance_analyzer = performance_analyzer
        self.thread_pool = thread_pool or ThreadPoolExecutor(max_workers=4)
        self.mutation_config = MutationConfig()
        
    async def select_top_performers(self, strategies: List[Dict], selection_pressure: float = 0.3) -> List[Dict]:
        """Select top performing strategies based on real performance metrics"""
        try:
            if not strategies:
                return []
            
            # Get performance metrics for all strategies
            strategy_performances = []
            
            for strategy in strategies:
                strategy_id = strategy.get('strategy_id', 'unknown')
                
                # Get cached performance metrics
                if strategy_id in self.performance_analyzer.performance_cache:
                    metrics = self.performance_analyzer.performance_cache[strategy_id]
                    strategy_performances.append((strategy, metrics.composite_score))
                else:
                    # No performance data - assign low score
                    strategy_performances.append((strategy, -10.0))
            
            if not strategy_performances:
                raise ValueError("No strategies with performance data available for selection")
            
            # Sort by performance score (descending)
            strategy_performances.sort(key=lambda x: x[1], reverse=True)
            
            # Select top performers
            num_to_select = max(2, int(len(strategies) * selection_pressure))
            top_performers = [strategy for strategy, score in strategy_performances[:num_to_select]]
            
            logger.info(f"[SELECTION] Selected {len(top_performers)} top performers from {len(strategies)} strategies")
            return top_performers
            
        except Exception as e:
            logger.error(f"[ERROR] Top performer selection failed: {e}")
            raise RuntimeError(f"Selection failed: {e}")
    
    async def select_crossover_parents(self, parents: List[Dict]) -> Tuple[Dict, Dict]:
        """Select two parents for crossover based on performance diversity"""
        try:
            if len(parents) < 2:
                raise ValueError("Need at least 2 parents for crossover")
            
            # Get performance scores for all parents
            parent_scores = []
            for parent in parents:
                strategy_id = parent.get('strategy_id', 'unknown')
                if strategy_id in self.performance_analyzer.performance_cache:
                    metrics = self.performance_analyzer.performance_cache[strategy_id]
                    parent_scores.append((parent, metrics.composite_score))
                else:
                    parent_scores.append((parent, 0.0))
            
            # Sort by performance (descending)
            parent_scores.sort(key=lambda x: x[1], reverse=True)
            
            # Select first parent from top performers
            parent1 = parent_scores[0][0]
            
            # Select second parent with different performance characteristics for diversity
            parent1_score = parent_scores[0][1]
            max_difference = 0
            parent2 = parent_scores[1][0] if len(parent_scores) > 1 else parent1
            
            for parent, score in parent_scores[1:]:
                difference = abs(score - parent1_score)
                if difference > max_difference:
                    max_difference = difference
                    parent2 = parent
            
            return parent1, parent2
            
        except Exception as e:
            logger.error(f"[ERROR] Crossover parent selection failed: {e}")
            return parents[0], parents[1] if len(parents) > 1 else parents[0]
    
    async def create_crossover_strategies(self, parents: List[Dict], max_offspring: int = 10) -> List[Dict]:
        """Create new strategies through performance-based crossover"""
        try:
            if len(parents) < 2:
                return []
            
            offspring = []
            num_offspring = min(max_offspring, max(1, int(len(parents) * 0.8)))
            
            for i in range(num_offspring):
                try:
                    # Select parents based on performance diversity
                    parent1, parent2 = await self.select_crossover_parents(parents)
                    
                    # Create crossover child
                    child = await self._crossover_strategies(parent1, parent2, i)
                    if child:
                        offspring.append(child)
                        
                except Exception as e:
                    logger.debug(f"[CROSSOVER] Failed to create offspring {i}: {e}")
                    continue
            
            logger.info(f"[CROSSOVER] Created {len(offspring)} offspring from {len(parents)} parents")
            return offspring
            
        except Exception as e:
            logger.error(f"[ERROR] Crossover strategy creation failed: {e}")
            return []
    
    async def _crossover_strategies(self, parent1: Dict, parent2: Dict, index: int) -> Optional[Dict]:
        """Create a single crossover strategy"""
        try:
            # Create child based on parent1 structure
            child = parent1.copy()
            
            # Generate unique ID
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            parent_hash = hashlib.md5(f"{parent1.get('strategy_id', '')}_{parent2.get('strategy_id', '')}".encode()).hexdigest()[:8]
            child['strategy_id'] = f"crossover_{parent_hash}_{index}"
            child['name'] = f"Crossover {parent1.get('name', 'P1')} x {parent2.get('name', 'P2')}"
            child['description'] = f"Crossover of {parent1.get('name', 'P1')} and {parent2.get('name', 'P2')}"
            child['parent_id'] = f"{parent1.get('strategy_id', '')}+{parent2.get('strategy_id', '')}"
            child['created_at'] = datetime.now().isoformat()
            
            # Crossover parameters (take from both parents)
            child['parameters'] = {**parent1.get('parameters', {}), **parent2.get('parameters', {})}
            
            # Crossover conditions (combine from both parents)
            p1_entry = parent1.get('entry_conditions', [])
            p2_entry = parent2.get('entry_conditions', [])
            child['entry_conditions'] = list(set(p1_entry + p2_entry))  # Unique conditions
            
            p1_exit = parent1.get('exit_conditions', [])
            p2_exit = parent2.get('exit_conditions', [])
            child['exit_conditions'] = list(set(p1_exit + p2_exit))  # Unique conditions
            
            # Risk management (average of both parents)
            p1_risk = parent1.get('risk_management', {})
            p2_risk = parent2.get('risk_management', {})
            child['risk_management'] = self._average_risk_management(p1_risk, p2_risk)
            
            # Add crossover tags
            child['tags'] = list(set(
                parent1.get('tags', []) + 
                parent2.get('tags', []) + 
                [f'crossover_{index}', 'performance_based_crossover']
            ))
            
            return child
            
        except Exception as e:
            logger.error(f"[ERROR] Strategy crossover failed: {e}")
            return None
    
    def _average_risk_management(self, risk1: Dict, risk2: Dict) -> Dict:
        """Average risk management parameters from two parents"""
        try:
            averaged_risk = {}
            
            # Average numeric values
            for key in ['stop_loss', 'take_profit', 'position_size']:
                val1 = risk1.get(key, 0)
                val2 = risk2.get(key, 0)
                if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                    averaged_risk[key] = (val1 + val2) / 2
                elif val1:
                    averaged_risk[key] = val1
                elif val2:
                    averaged_risk[key] = val2
            
            # Take non-numeric values from first parent
            for key, value in risk1.items():
                if key not in averaged_risk and not isinstance(value, (int, float)):
                    averaged_risk[key] = value
            
            return averaged_risk
            
        except Exception as e:
            logger.error(f"[ERROR] Risk management averaging failed: {e}")
            return risk1 or risk2 or {}
    
    async def apply_mutations(self, strategy: Dict, mutation_type: str = 'adaptive') -> Dict:
        """Apply data-driven mutations to a strategy"""
        try:
            strategy_id = strategy.get('strategy_id', 'unknown')
            
            # Get performance data for adaptive mutation
            performance_factor = self._get_performance_factor(strategy_id)
            
            # Apply mutations based on type and performance
            if mutation_type == 'aggressive':
                await self._apply_aggressive_mutations(strategy, performance_factor)
            elif mutation_type == 'conservative':
                await self._apply_conservative_mutations(strategy, performance_factor)
            elif mutation_type == 'risk_reduction':
                await self._apply_risk_reduction_mutations(strategy, performance_factor)
            else:  # adaptive
                await self._apply_adaptive_mutations(strategy, performance_factor)
            
            # Add mutation tags
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            mutation_hash = hashlib.md5(f"{strategy_id}_{mutation_type}_{timestamp}".encode()).hexdigest()[:8]
            
            if 'tags' not in strategy:
                strategy['tags'] = []
            strategy['tags'].append(f'mutation_{mutation_type}_{mutation_hash}')
            
            logger.debug(f"[MUTATION] Applied {mutation_type} mutations to {strategy_id}")
            return strategy
            
        except Exception as e:
            logger.error(f"[ERROR] Mutation application failed: {e}")
            return strategy
    
    def _get_performance_factor(self, strategy_id: str) -> float:
        """Get performance factor for adaptive mutations"""
        try:
            if strategy_id in self.performance_analyzer.performance_cache:
                metrics = self.performance_analyzer.performance_cache[strategy_id]
                
                # Calculate performance factor (0.0 to 1.0)
                # Poor performance -> higher factor (more aggressive mutations)
                # Good performance -> lower factor (conservative mutations)
                
                if metrics.composite_score > 50:
                    return 0.2  # Conservative mutations for good performers
                elif metrics.composite_score > 0:
                    return 0.5  # Moderate mutations
                else:
                    return 0.8  # Aggressive mutations for poor performers
            
            return 0.5  # Default moderate factor
            
        except Exception as e:
            logger.debug(f"[MUTATION] Failed to get performance factor: {e}")
            return 0.5
    
    async def _apply_adaptive_mutations(self, strategy: Dict, performance_factor: float):
        """Apply adaptive mutations based on performance"""
        try:
            # Mutate parameters based on performance
            if random.random() < 0.6:
                self._mutate_entry_conditions(strategy, performance_factor)
            
            if random.random() < 0.5:
                self._mutate_risk_parameters(strategy, performance_factor)
            
            if random.random() < 0.3:
                self._mutate_timeframe(strategy)
                
        except Exception as e:
            logger.error(f"[ERROR] Adaptive mutations failed: {e}")
    
    async def _apply_conservative_mutations(self, strategy: Dict, performance_factor: float):
        """Apply conservative mutations"""
        try:
            # Small parameter changes
            factor = min(performance_factor * 0.5, 0.2)
            
            if random.random() < 0.3:
                self._mutate_entry_conditions(strategy, factor)
            
            if random.random() < 0.4:
                self._mutate_risk_parameters(strategy, factor)
                
        except Exception as e:
            logger.error(f"[ERROR] Conservative mutations failed: {e}")
    
    async def _apply_aggressive_mutations(self, strategy: Dict, performance_factor: float):
        """Apply aggressive mutations"""
        try:
            # Large parameter changes
            factor = min(performance_factor * 1.5, 0.8)
            
            if random.random() < 0.8:
                self._mutate_entry_conditions(strategy, factor)
            
            if random.random() < 0.7:
                self._mutate_risk_parameters(strategy, factor)
            
            if random.random() < 0.5:
                self._mutate_timeframe(strategy)
                
        except Exception as e:
            logger.error(f"[ERROR] Aggressive mutations failed: {e}")
    
    async def _apply_risk_reduction_mutations(self, strategy: Dict, performance_factor: float):
        """Apply risk reduction focused mutations"""
        try:
            # Focus on reducing risk
            risk_mgmt = strategy.get('risk_management', {}).copy()
            
            # Tighten stop losses
            if 'stop_loss' in risk_mgmt:
                current_sl = float(risk_mgmt['stop_loss'])
                new_sl = current_sl * (1 - performance_factor * 0.3)  # Reduce stop loss
                risk_mgmt['stop_loss'] = max(0.005, min(0.1, new_sl))
            
            # Reduce position size
            if 'position_size' in risk_mgmt:
                current_size = float(risk_mgmt['position_size'])
                new_size = current_size * (1 - performance_factor * 0.2)  # Reduce position size
                risk_mgmt['position_size'] = max(0.01, min(0.1, new_size))
            
            strategy['risk_management'] = risk_mgmt
            
        except Exception as e:
            logger.error(f"[ERROR] Risk reduction mutations failed: {e}")
    
    def _mutate_entry_conditions(self, strategy: Dict, factor: float):
        """Mutate entry conditions"""
        try:
            conditions = strategy.get('entry_conditions', []).copy()
            new_conditions = []
            
            for condition in conditions:
                # Mutate RSI parameters
                rsi_match = re.search(r'rsi[_\(](\d+)', condition.lower())
                if rsi_match:
                    current_period = int(rsi_match.group(1))
                    change = int(current_period * factor * (random.random() - 0.5) * 2)
                    new_period = max(self.mutation_config.rsi_range[0], 
                                   min(self.mutation_config.rsi_range[1], current_period + change))
                    condition = re.sub(r'rsi[_\(](\d+)', f'rsi_{new_period}', condition, flags=re.IGNORECASE)
                
                new_conditions.append(condition)
            
            strategy['entry_conditions'] = new_conditions
            
        except Exception as e:
            logger.error(f"[ERROR] Entry condition mutation failed: {e}")
    
    def _mutate_risk_parameters(self, strategy: Dict, factor: float):
        """Mutate risk management parameters"""
        try:
            risk_mgmt = strategy.get('risk_management', {}).copy()
            
            # Mutate stop loss
            if 'stop_loss' in risk_mgmt:
                current_sl = float(risk_mgmt['stop_loss'])
                change = current_sl * factor * (random.random() - 0.5) * 2
                new_sl = current_sl + change
                risk_mgmt['stop_loss'] = max(0.005, min(0.1, new_sl))
            
            # Mutate take profit
            if 'take_profit' in risk_mgmt:
                current_tp = float(risk_mgmt['take_profit'])
                change = current_tp * factor * (random.random() - 0.5) * 2
                new_tp = current_tp + change
                risk_mgmt['take_profit'] = max(0.01, min(0.2, new_tp))
            
            strategy['risk_management'] = risk_mgmt
            
        except Exception as e:
            logger.error(f"[ERROR] Risk parameter mutation failed: {e}")
    
    def _mutate_timeframe(self, strategy: Dict):
        """Mutate strategy timeframe"""
        try:
            if random.random() < 0.3:  # 30% chance to change timeframe
                strategy['timeframe'] = random.choice(self.mutation_config.timeframe_options)
                
        except Exception as e:
            logger.error(f"[ERROR] Timeframe mutation failed: {e}")
