#!/usr/bin/env python3
"""
Options Strategy Evolution Module

This module provides a modular approach to options strategy evolution using genetic algorithms.
It breaks down the complex evolution process into focused, maintainable components.

Modules:
- data_integration: Efficient data loading and backtesting integration
- performance_analysis: Strategy performance evaluation and metrics
- genetic_operations: Selection, crossover, and mutation operations
- strategy_management: Strategy registry and lifecycle management
- evolution_core: Main evolution orchestration
"""

from .data_integration import DataIntegrationManager
from .performance_analysis import PerformanceAnalyzer
from .genetic_operations import GeneticOperations
from .strategy_management import StrategyManager
from .evolution_core import EvolutionCore

__all__ = [
    'DataIntegrationManager',
    'PerformanceAnalyzer', 
    'GeneticOperations',
    'StrategyManager',
    'EvolutionCore'
]

__version__ = "1.0.0"
