exported_at: '2025-08-17T22:19:22.521440'
strategies:
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_150
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_151
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_152
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_153
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_154
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_155
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_156
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_157
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_158
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_159
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_160
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_161
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_162
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_163
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_164
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_165
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_166
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_167
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_168
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_169
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_170
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_171
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_172
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_173
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_174
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_175
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_176
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_177
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_178
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_179
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_180
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_181
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_182
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_183
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_184
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_185
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_186
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_187
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_188
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_189
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_190
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_191
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_192
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_193
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_194
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_195
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_196
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_197
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_198
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:17:31.109818'
  description: ''
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Backtesting Strategy
  parameters: {}
  parent_id: null
  risk_management: {}
  status: experimental
  strategy_id: bt_strategy_199
  tags: []
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.179437'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.005
    take_profit: 0.01
  status: experimental
  strategy_id: crossover_ff59009b_0
  tags:
  - crossover_0
  - performance_based_crossover
  - mutation_adaptive_086c9458
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.179437'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_1
  tags:
  - performance_based_crossover
  - crossover_1
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.179437'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_2
  tags:
  - crossover_2
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.179437'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_3
  tags:
  - performance_based_crossover
  - crossover_3
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.179437'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_4
  tags:
  - crossover_4
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.179437'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_5
  tags:
  - performance_based_crossover
  - crossover_5
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.179437'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_6
  tags:
  - performance_based_crossover
  - crossover_6
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_7
  tags:
  - performance_based_crossover
  - crossover_7
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_8
  tags:
  - performance_based_crossover
  - crossover_8
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_9
  tags:
  - performance_based_crossover
  - crossover_9
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_10
  tags:
  - performance_based_crossover
  - crossover_10
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_11
  tags:
  - performance_based_crossover
  - crossover_11
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_12
  tags:
  - crossover_12
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_13
  tags:
  - crossover_13
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.005
    take_profit: 0.01
  status: experimental
  strategy_id: crossover_ff59009b_14
  tags:
  - performance_based_crossover
  - crossover_14
  - mutation_adaptive_829dd759
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_15
  tags:
  - performance_based_crossover
  - crossover_15
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_16
  tags:
  - performance_based_crossover
  - crossover_16
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_17
  tags:
  - crossover_17
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_18
  tags:
  - crossover_18
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_19
  tags:
  - crossover_19
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_20
  tags:
  - crossover_20
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_21
  tags:
  - performance_based_crossover
  - crossover_21
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_22
  tags:
  - crossover_22
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_23
  tags:
  - crossover_23
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_24
  tags:
  - performance_based_crossover
  - crossover_24
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.005
    take_profit: 0.01
  status: experimental
  strategy_id: crossover_ff59009b_25
  tags:
  - performance_based_crossover
  - crossover_25
  - mutation_aggressive_0bf7679d
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_26
  tags:
  - crossover_26
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_27
  tags:
  - crossover_27
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.180438'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_28
  tags:
  - performance_based_crossover
  - crossover_28
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_29
  tags:
  - crossover_29
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_30
  tags:
  - performance_based_crossover
  - crossover_30
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_31
  tags:
  - performance_based_crossover
  - crossover_31
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_32
  tags:
  - crossover_32
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.005
    take_profit: 0.01
  status: experimental
  strategy_id: crossover_ff59009b_33
  tags:
  - performance_based_crossover
  - crossover_33
  - mutation_conservative_e3efe364
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_34
  tags:
  - crossover_34
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_35
  tags:
  - crossover_35
  - performance_based_crossover
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_36
  tags:
  - performance_based_crossover
  - crossover_36
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_37
  tags:
  - performance_based_crossover
  - crossover_37
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_38
  tags:
  - performance_based_crossover
  - crossover_38
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_39
  tags:
  - performance_based_crossover
  - crossover_39
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_40
  tags:
  - performance_based_crossover
  - crossover_40
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_41
  tags:
  - performance_based_crossover
  - crossover_41
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_42
  tags:
  - performance_based_crossover
  - crossover_42
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_43
  tags:
  - performance_based_crossover
  - crossover_43
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_44
  tags:
  - performance_based_crossover
  - crossover_44
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_45
  tags:
  - performance_based_crossover
  - crossover_45
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_46
  tags:
  - performance_based_crossover
  - crossover_46
  - mutation_conservative_5cf6b980
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_47
  tags:
  - performance_based_crossover
  - crossover_47
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_48
  tags:
  - performance_based_crossover
  - crossover_48
  - mutation_aggressive_afdffec4
  timeframe: 5min
  version: v1
  volatility_outlook: normal
- created_at: '2025-08-17T22:19:20.181440'
  description: Crossover of Backtesting Strategy and Backtesting Strategy
  entry_conditions: []
  exit_conditions: []
  market_outlook: neutral
  name: Crossover Backtesting Strategy x Backtesting Strategy
  parameters: {}
  parent_id: bt_strategy_100+bt_strategy_101
  risk_management:
    position_size: 0.0
    stop_loss: 0.0
    take_profit: 0.0
  status: experimental
  strategy_id: crossover_ff59009b_49
  tags:
  - performance_based_crossover
  - crossover_49
  timeframe: 5min
  version: v1
  volatility_outlook: normal
total_strategies: 100
