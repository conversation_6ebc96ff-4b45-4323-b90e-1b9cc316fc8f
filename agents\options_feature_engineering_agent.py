#!/usr/bin/env python3
"""
Options Feature Engineering Agent - Greeks, Volatility & Technical Features

Features:
📊 1. Options Greeks Calculation
- Delta, Gamma, Theta, Vega, Rho calculations
- Greeks sensitivity analysis
- Portfolio Greeks aggregation
- Greeks-based risk metrics

📈 2. Volatility Modeling
- Implied volatility calculation
- Historical volatility analysis
- Volatility surface construction
- Volatility smile/skew analysis

⚡ 3. Technical Features
- Options flow indicators
- Put-Call ratio analysis
- Open interest analysis
- Volume-weighted metrics

🎯 4. High-Performance Processing
- Vectorized Greeks calculations
- Polars + PyArrow optimization
- GPU-accelerated computations
- Memory-efficient processing
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
import pyarrow.compute as pc
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import aiofiles
from dataclasses import dataclass
import json
import math

# Options pricing libraries
try:
    import py_vollib.black_scholes as bs
    import py_vollib.black_scholes.greeks.analytical as greeks
    from py_vollib.black_scholes.implied_volatility import implied_volatility
except ImportError:
    pass

# Use polars-talib for technical indicators
try:
    import polars_talib as ta
except ImportError:
    ta = None

logger = logging.getLogger(__name__)

@dataclass
class GreeksData:
    """Greeks calculation results"""
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float
    implied_vol: float

@dataclass
class VolatilityMetrics:
    """Volatility analysis results"""
    historical_vol: float
    implied_vol: float
    vol_rank: float
    vol_percentile: float
    vol_skew: float

class OptionsFeatureEngineeringAgent:
    """
    Options Feature Engineering Agent for calculating Greeks, volatility, and technical features
    
    Handles:
    - Options Greeks calculations (Delta, Gamma, Theta, Vega, Rho)
    - Implied and historical volatility analysis
    - Volatility surface construction
    - Options flow and sentiment indicators
    - Technical analysis on underlying assets
    """
    
    def __init__(self, config_path: str = "config/options_feature_engineering_config.yaml"):
        """Initialize Options Feature Engineering Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        
        # Data paths for multi-timeframe processing
        self.data_path = Path("data")
        self.historical_path = self.data_path / "historical"
        self.live_path = self.data_path / "live"
        self.features_path = self.data_path / "features"
        self.greeks_path = self.data_path / "greeks"
        self.volatility_path = self.data_path / "volatility"

        # Multi-timeframe support
        self.timeframes = ["1min", "3min", "5min", "15min"]

        # Create directories for each timeframe
        for timeframe in self.timeframes:
            (self.features_path / timeframe).mkdir(parents=True, exist_ok=True)
            (self.greeks_path / timeframe).mkdir(parents=True, exist_ok=True)
            (self.volatility_path / timeframe).mkdir(parents=True, exist_ok=True)
        
        # Risk-free rate (can be updated from market data)
        self.risk_free_rate = 0.06  # 6% annual
        
        logger.info("[INIT] Options Feature Engineering Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent with optional parameters"""
        try:
            # Load configuration
            await self._load_config()
            
            # Store kwargs for later use
            self.init_kwargs = kwargs
            
            logger.info("[SUCCESS] Options Feature Engineering Agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from file"""
        try:
            # Default configuration
            self.config = {
                'underlying_symbols': ['NIFTY', 'BANKNIFTY'],
                'lookback_days': 30,
                'volatility_window': 20,
                'risk_free_rate': 0.06,
                'chunk_size': 10000
            }
            logger.info("[CONFIG] Configuration loaded successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load configuration: {e}")
            raise
    
    async def start(self, **kwargs) -> bool:
        """Enhanced start method with comprehensive feature engineering"""
        try:
            logger.info("[START] Starting Enhanced Options Feature Engineering Agent...")
            
            self.is_running = True

            # Check if we're in skip_historical mode
            skip_historical = kwargs.get('skip_historical', False)
            
            if skip_historical:
                logger.info("[MODE] Skip historical mode - processing existing data for feature engineering")
                # Process all available data files
                await self._process_all_available_data()
                logger.info("[SUCCESS] Multi-timeframe feature engineering completed")
                return True
            else:
                # Extract date parameters if provided (for training pipeline)
                from_date = kwargs.get('from_date')
                to_date = kwargs.get('to_date')
                
                if from_date and to_date:
                    logger.info("[MODE] Training pipeline mode - processing historical data")
                    # Process historical data with comprehensive features
                    success = await self._process_comprehensive_features(from_date, to_date)
                    return success
                else:
                    logger.info("[MODE] Live mode - processing real-time features")
                    # Process all available data files
                    await self._process_all_available_data()
                    logger.info("[SUCCESS] Multi-timeframe feature engineering completed")
                    return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False
    
    async def _process_comprehensive_features(self, from_date: str, to_date: str) -> bool:
        """Process comprehensive features for training pipeline"""
        try:
            logger.info("[COMPREHENSIVE] Starting comprehensive feature engineering...")
            
            # Process features for each underlying and timeframe
            for underlying in self.config['underlying_symbols']:
                logger.info(f"[COMPREHENSIVE] Processing {underlying} features...")
                
                for timeframe in self.timeframes:
                    logger.info(f"[COMPREHENSIVE] Processing {timeframe} features for {underlying}...")
                    
                    # Load historical data
                    historical_data = await self._load_historical_data(underlying, timeframe)
                    
                    if historical_data is None or historical_data.height == 0:
                        logger.warning(f"[WARNING] No {timeframe} data found for {underlying}")
                        continue
                    
                    # Load index data for this underlying
                    index_data = await self._load_index_data(underlying, timeframe)
                    
                    # Calculate comprehensive features
                    features = await self._calculate_comprehensive_features(
                        historical_data, index_data, underlying, timeframe
                    )
                    
                    if features is not None:
                        # Save features with proper naming
                        await self._save_comprehensive_features(features, underlying, timeframe)
                        logger.info(f"[SUCCESS] Processed {features.height} feature records for {underlying} {timeframe}")
            
            logger.info("[SUCCESS] Comprehensive feature engineering completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Comprehensive feature engineering failed: {e}")
            return False
    
    async def _load_historical_data(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load historical data for comprehensive feature engineering"""
        try:
            # Look for historical data files
            data_path = self.historical_path / timeframe
            
            # Try different patterns for options data
            patterns = [
                f"{underlying}_*_{timeframe.upper()}_*.parquet",
                f"{underlying}_*_*.parquet",
                f"*{underlying}*.parquet"
            ]
            
            all_files = []
            for pattern in patterns:
                files = list(data_path.glob(pattern))
                all_files.extend(files)
            
            if not all_files:
                logger.warning(f"[LOAD] No historical {timeframe} data found for {underlying}")
                return None
            
            # Load and combine all files with column alignment
            dataframes = []
            all_columns = set()

            # First pass: collect all unique columns and load dataframes
            for file in all_files:
                try:
                    df = pl.read_parquet(file)

                    # Skip index files when looking for options data
                    if 'Index_' in str(file.name):
                        logger.debug(f"[LOAD] Skipping index file: {file.name}")
                        continue

                    # Filter by underlying if column exists
                    if 'underlying' in df.columns:
                        df = df.filter(pl.col('underlying') == underlying)
                        # Skip if no data after filtering
                        if df.height == 0:
                            continue

                    # Collect columns
                    all_columns.update(df.columns)
                    dataframes.append(df)

                except Exception as e:
                    logger.warning(f"[LOAD] Failed to load {file}: {e}")
                    continue

            if not dataframes:
                logger.warning(f"[LOAD] No valid options data files found for {underlying} {timeframe}")
                return None

            # Combine all dataframes using diagonal concatenation to merge schemas
            combined_df = pl.concat(dataframes, how="diagonal")

            logger.info(f"[LOAD] Loaded {combined_df.height} historical records for {underlying} {timeframe}")
            logger.info(f"[LOAD] Combined schema has {len(combined_df.columns)} columns: {combined_df.columns}")

            # Ensure essential columns like 'strike_price' are present
            if 'strike_price' not in combined_df.columns:
                logger.warning(f"[WARNING] 'strike_price' column not found in the combined data for {underlying} {timeframe}. This will be an issue for the training agent.")
            else:
                # Forward fill strike prices to handle missing values
                combined_df = combined_df.with_columns(pl.col("strike_price").forward_fill())

            return combined_df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load historical data for {underlying} {timeframe}: {e}")
            logger.error(f"[ERROR] Files found: {[f.name for f in all_files]}")
            return None
    
    async def _load_index_data(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load index data for the underlying"""
        try:
            data_path = self.historical_path / timeframe
            
            # Look for index data files
            pattern = f"Index_{underlying}_{timeframe.upper()}_*.parquet"
            files = list(data_path.glob(pattern))
            
            if not files:
                # Try alternative patterns
                pattern = f"Index_{underlying}_*.parquet"
                files = list(data_path.glob(pattern))
            
            if not files:
                logger.warning(f"[LOAD] No index data found for {underlying}")
                return None
            
            # Load the most recent file
            latest_file = max(files, key=lambda x: x.stat().st_mtime)
            df = pl.read_parquet(latest_file)
            
            logger.info(f"[LOAD] Loaded {df.height} index records for {underlying}")
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load index data: {e}")
            return None
    
    async def _calculate_comprehensive_features(self, options_data: pl.DataFrame, index_data: Optional[pl.DataFrame], 
                                              underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Calculate comprehensive features based on ml-agent-feature.txt"""
        try:
            logger.info(f"[FEATURES] Calculating comprehensive features for {underlying} {timeframe}...")
            
            # Start with options data
            features_df = options_data.clone()

            # Ensure strike_price is float for consistent data types across all files
            if 'strike_price' in features_df.columns:
                features_df = features_df.with_columns(
                    pl.col('strike_price').cast(pl.Float64, strict=False)
                )

            # Add strategy_prefix for joining with backtest results
            features_df = features_df.with_columns(
                pl.lit("LC").alias("strategy_prefix")
            )

            # Construct strategy_id, which is essential for joining with backtest results
            # Only create strategy_id for rows that have valid strike_price (options data, not index data)
            if 'strike_price' in features_df.columns and 'timestamp' in features_df.columns:
                features_df = features_df.with_columns(
                    pl.when(pl.col("strike_price").is_not_null())
                    .then(
                        pl.col("strategy_prefix") + "_" +
                        pl.lit(underlying) + "_" +
                        pl.col("strike_price").cast(pl.Int64).cast(pl.Utf8) + "_" +
                        pl.col("timestamp").dt.strftime("%Y%m%d%H%M%S")
                    )
                    .otherwise(None)
                    .alias("strategy_id")
                )
            
            # Add basic technical indicators
            if 'close' in features_df.columns:
                features_df = features_df.with_columns([
                    pl.col('close').rolling_mean(window_size=10).alias('sma_10'),
                    pl.col('close').rolling_mean(window_size=20).alias('sma_20'),
                    pl.col('close').rolling_std(window_size=20).alias('volatility_20d'),
                    (pl.col('close').pct_change() * 100).alias('returns_pct')
                ])
            
            # Add timestamp-based features
            if 'timestamp' in features_df.columns:
                features_df = features_df.with_columns([
                    pl.col('timestamp').dt.hour().alias('hour'),
                    pl.col('timestamp').dt.weekday().alias('weekday'),
                    pl.col('timestamp').dt.month().alias('month')
                ])
            
            logger.info(f"[SUCCESS] Calculated {features_df.width} features for {underlying} {timeframe}")
            return features_df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate comprehensive features: {e}")
            return None
    
    async def _save_comprehensive_features(self, features: pl.DataFrame, underlying: str, timeframe: str):
        """Save comprehensive features with proper naming"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{underlying}_{timeframe}_features_{timestamp}.parquet"
            filepath = self.features_path / timeframe / filename
            
            # Save with brotli compression
            features.write_parquet(filepath, compression="brotli")
            logger.info(f"[SAVE] Saved {features.height} feature records to {filename}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save comprehensive features: {e}")
    
    async def _process_all_available_data(self):
        """Process features for all available data files"""
        try:
            for timeframe in self.timeframes:
                timeframe_path = self.historical_path / timeframe
                if not timeframe_path.exists():
                    continue
                    
                # Get all parquet files in this timeframe directory
                all_files = list(timeframe_path.glob("*.parquet"))
                
                # Group files by underlying symbol
                symbol_files = {}
                for file in all_files:
                    # Extract symbol from filename
                    filename = file.name
                    if filename.startswith('Index_'):
                        # Index files: Index_NIFTY_1MIN_*.parquet
                        symbol = filename.split('_')[1]
                    elif '_' in filename:
                        # Options files: NIFTY_25000_CE_1MIN_*.parquet
                        symbol = filename.split('_')[0]
                    else:
                        continue
                        
                    if symbol not in symbol_files:
                        symbol_files[symbol] = []
                    symbol_files[symbol].append(file)
                
                # Process each symbol
                for symbol, files in symbol_files.items():
                    await self._process_symbol_files(symbol, timeframe, files)
                    
        except Exception as e:
            logger.error(f"[ERROR] Failed to process all available data: {e}")
    
    async def _process_symbol_files(self, symbol: str, timeframe: str, files: list):
        """Process all files for a specific symbol and timeframe"""
        try:
            logger.info(f"[PROCESS] Processing {len(files)} {timeframe} files for {symbol}...")
            
            all_dataframes = []
            
            for file in files:
                try:
                    df = pl.read_parquet(file)
                    if df.height > 0:
                        all_dataframes.append(df)
                except Exception as e:
                    logger.warning(f"[WARNING] Failed to load {file.name}: {e}")
                    continue
            
            if not all_dataframes:
                logger.warning(f"[WARNING] No valid data files found for {symbol} {timeframe}")
                return
            
            # Combine all dataframes
            combined_df = pl.concat(all_dataframes, how="diagonal")
            
            # Calculate features
            features = await self._calculate_comprehensive_features(combined_df, None, symbol, timeframe)
            
            # Save features
            await self._save_timeframe_features(symbol, timeframe, features)
            
            logger.info(f"[SUCCESS] {timeframe} features processed for {symbol}: {features.height} total records")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to process {symbol} {timeframe} files: {e}")

    async def _load_timeframe_data(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load data for specific underlying and timeframe"""
        try:
            # Check both historical and live data
            timeframe_path = self.historical_path / timeframe
            
            # Convert timeframe to uppercase for file pattern matching
            timeframe_upper = timeframe.upper()
            
            # Try different file patterns based on actual file naming
            if underlying in ['NIFTY', 'BANKNIFTY']:
                # For index data, look for Index_UNDERLYING_TIMEFRAME pattern
                patterns = [
                    f"Index_{underlying}_{timeframe_upper}_*.parquet",
                    f"{underlying}_{timeframe_upper}_*.parquet",
                    f"*{underlying}*{timeframe_upper}*.parquet"
                ]
            else:
                # For options data, look for UNDERLYING_STRIKE_TYPE_TIMEFRAME pattern
                patterns = [
                    f"{underlying}_*_{timeframe_upper}_*.parquet",
                    f"*{underlying}*{timeframe_upper}*.parquet"
                ]

            files = []
            for pattern in patterns:
                found_files = list(timeframe_path.glob(pattern))
                if found_files:
                    files.extend(found_files)

            if not files:
                # Try live data if no historical data
                timeframe_path = self.live_path / timeframe
                for pattern in patterns:
                    found_files = list(timeframe_path.glob(pattern))
                    if found_files:
                        files.extend(found_files)

            if not files:
                logger.warning(f"[WARNING] No {timeframe} data files found for {underlying}")
                return None

            # Load and combine all files for this underlying
            all_dataframes = []
            for file in files:
                try:
                    df = pl.read_parquet(file)
                    if df.height > 0:
                        all_dataframes.append(df)
                except Exception as e:
                    logger.warning(f"[WARNING] Failed to load {file.name}: {e}")
                    continue
            
            if not all_dataframes:
                logger.warning(f"[WARNING] No valid data files found for {underlying} {timeframe}")
                return None
            
            # Combine all dataframes
            combined_df = pl.concat(all_dataframes)

            # Filter for the specific underlying if the file contains multiple symbols
            if 'symbol' in combined_df.columns:
                combined_df = combined_df.filter(pl.col('symbol') == underlying)
            elif 'underlying' in combined_df.columns:
                combined_df = combined_df.filter(pl.col('underlying') == underlying)

            if combined_df.height == 0:
                logger.warning(f"[WARNING] No {timeframe} data found for {underlying} after filtering")
                return None

            logger.info(f"[LOAD] Loaded {timeframe} data for {underlying}: {combined_df.height} records from {len(files)} files")
            return combined_df

        except Exception as e:
            logger.error(f"[ERROR] Failed to load {timeframe} data for {underlying}: {e}")
            return None
    
    async def _calculate_basic_features(self, data: pl.DataFrame, underlying: str, timeframe: str) -> pl.DataFrame:
        """Calculate basic features for the data"""
        try:
            features = data.clone()

            # Ensure strike_price is float for consistent data types across all files
            if 'strike_price' in features.columns:
                features = features.with_columns(
                    pl.col('strike_price').cast(pl.Float64, strict=False)
                )

            # Add strategy_prefix for joining with backtest results
            features = features.with_columns(
                pl.lit("LC").alias("strategy_prefix")
            )

            # Construct strategy_id for joining with backtest results
            # Only create strategy_id for rows that have valid strike_price (options data, not index data)
            if 'strike_price' in features.columns and 'timestamp' in features.columns:
                features = features.with_columns(
                    pl.when(pl.col("strike_price").is_not_null())
                    .then(
                        pl.col("strategy_prefix") + "_" +
                        pl.lit(underlying) + "_" +
                        pl.col("strike_price").cast(pl.Int64).cast(pl.Utf8) + "_" +
                        pl.col("timestamp").dt.strftime("%Y%m%d%H%M%S")
                    )
                    .otherwise(None)
                    .alias("strategy_id")
                )
            
            # Add basic technical indicators if close price exists
            if 'close' in features.columns:
                features = features.with_columns([
                    pl.col('close').rolling_mean(window_size=10).alias('sma_10'),
                    pl.col('close').rolling_mean(window_size=20).alias('sma_20'),
                    pl.col('close').rolling_std(window_size=20).alias('volatility_20d'),
                    (pl.col('close').pct_change() * 100).alias('returns_pct')
                ])
            
            # Add volume features if volume exists
            if 'volume' in features.columns:
                features = features.with_columns([
                    pl.col('volume').rolling_mean(window_size=20).alias('volume_sma_20')
                ])
            
            # Add timestamp features if timestamp exists
            if 'timestamp' in features.columns:
                features = features.with_columns([
                    pl.col('timestamp').dt.hour().alias('hour'),
                    pl.col('timestamp').dt.weekday().alias('weekday')
                ])
            
            return features
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to calculate basic features: {e}")
            return data
    
    async def _save_timeframe_features(self, underlying: str, timeframe: str, features_data: pl.DataFrame):
        """Save engineered features for specific timeframe"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # Save main features
            features_file = self.features_path / timeframe / f"{underlying}_{timeframe}_features_{timestamp}.parquet"
            features_data.write_parquet(features_file, compression="snappy")

            logger.info(f"[SAVE] {timeframe} features saved for {underlying}: {features_data.height} records")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save {timeframe} features for {underlying}: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Feature Engineering Agent...")
            self.is_running = False
            logger.info("[SUCCESS] Options Feature Engineering Agent cleaned up")
            
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    """Example usage of Options Feature Engineering Agent"""
    agent = OptionsFeatureEngineeringAgent()
    
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
