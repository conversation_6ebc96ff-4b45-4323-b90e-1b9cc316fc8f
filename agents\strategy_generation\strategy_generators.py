import logging
import polars as pl
from datetime import datetime
from typing import Dict, List, Any
from agents.strategy_generation.data_models import OptionsLeg, OptionsStrategy, StrategyType
from agents.strategy_generation.strategy_calculators import StrategyCalculators

logger = logging.getLogger(__name__)

class StrategyGenerators:
    """Generates various options strategies."""

    def __init__(self, config: Dict, calculators: StrategyCalculators):
        self.config = config
        self.calculators = calculators

    async def generate_strategy_type(self, strategy_type: StrategyType, underlying: str,
                                    option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate strategies of specific type."""
        try:
            strategies = []

            # Basic directional buying strategies
            if strategy_type == StrategyType.LONG_CALL:
                strategies = await self._generate_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.LONG_PUT:
                strategies = await self._generate_long_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.PROTECTIVE_PUT:
                strategies = await self._generate_protective_put_strategies(underlying, option_chain, spot_price)

            # ATM buying strategies
            elif strategy_type == StrategyType.ATM_LONG_CALL:
                strategies = await self._generate_atm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.ATM_LONG_PUT:
                strategies = await self._generate_atm_long_put_strategies(underlying, option_chain, spot_price)

            # OTM buying strategies
            elif strategy_type == StrategyType.OTM_LONG_CALL:
                strategies = await self._generate_otm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.OTM_LONG_PUT:
                strategies = await self._generate_otm_long_put_strategies(underlying, option_chain, spot_price)

            # Far OTM buying strategies
            elif strategy_type == StrategyType.FAR_OTM_LONG_CALL:
                strategies = await self._generate_far_otm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.FAR_OTM_LONG_PUT:
                strategies = await self._generate_far_otm_long_put_strategies(underlying, option_chain, spot_price)

            # Intraday buying strategies
            elif strategy_type == StrategyType.INTRADAY_SCALPING_CALL:
                strategies = await self._generate_intraday_scalping_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_SCALPING_PUT:
                strategies = await self._generate_intraday_scalping_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_MOMENTUM_CALL:
                strategies = await self._generate_intraday_momentum_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_MOMENTUM_PUT:
                strategies = await self._generate_intraday_momentum_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_REVERSAL_CALL:
                strategies = await self._generate_intraday_reversal_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.INTRADAY_REVERSAL_PUT:
                strategies = await self._generate_intraday_reversal_put_strategies(underlying, option_chain, spot_price)

            # Gamma Scalping (Long)
            elif strategy_type == StrategyType.GAMMA_SCALPING_LONG:
                strategies = await self._generate_gamma_scalping_long_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.DELTA_NEUTRAL_GAMMA_SCALP:
                strategies = await self._generate_delta_neutral_gamma_scalp_strategies(underlying, option_chain, spot_price)

            # Volatility Breakout (Long)
            elif strategy_type == StrategyType.VOLATILITY_BREAKOUT_LONG:
                strategies = await self._generate_volatility_breakout_long_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.VIX_BASED_STRATEGY:
                strategies = await self._generate_vix_based_strategy_strategies(underlying, option_chain, spot_price)

            # Volatility buying strategies
            elif strategy_type == StrategyType.LONG_STRADDLE:
                strategies = await self._generate_long_straddle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.LONG_STRANGLE:
                strategies = await self._generate_long_strangle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.REVERSE_IRON_CONDOR:
                strategies = await self._generate_reverse_iron_condor_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.REVERSE_IRON_BUTTERFLY:
                strategies = await self._generate_reverse_iron_butterfly_strategies(underlying, option_chain, spot_price)

            # Spread buying strategies (net debit spreads)
            elif strategy_type == StrategyType.BULL_CALL_SPREAD:
                strategies = await self._generate_bull_call_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.BEAR_PUT_SPREAD:
                strategies = await self._generate_bear_put_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.CALL_CALENDAR_SPREAD:
                strategies = await self._generate_call_calendar_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.PUT_CALENDAR_SPREAD:
                strategies = await self._generate_put_calendar_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.DIAGONAL_CALL_SPREAD:
                strategies = await self._generate_diagonal_call_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.DIAGONAL_PUT_SPREAD:
                strategies = await self._generate_diagonal_put_spread_strategies(underlying, option_chain, spot_price)

            # Ratio buying strategies (backspreads are net debit)
            elif strategy_type == StrategyType.RATIO_CALL_BACKSPREAD:
                strategies = await self._generate_ratio_call_backspread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.RATIO_PUT_BACKSPREAD:
                strategies = await self._generate_ratio_put_backspread_strategies(underlying, option_chain, spot_price)

            # Collar strategies (often net zero or small debit/credit)
            elif strategy_type == StrategyType.COLLAR:
                strategies = await self._generate_collar_strategies(underlying, option_chain, spot_price)

            # Synthetic buying strategies
            elif strategy_type == StrategyType.SYNTHETIC_LONG:
                strategies = await self._generate_synthetic_long_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SYNTHETIC_CALL:
                strategies = await self._generate_synthetic_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SYNTHETIC_PUT:
                strategies = await self._generate_synthetic_put_strategies(underlying, option_chain, spot_price)

            # Complex Multi-leg buying strategies
            elif strategy_type == StrategyType.BUTTERFLY_SPREAD:
                strategies = await self._generate_butterfly_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.CONDOR_SPREAD:
                strategies = await self._generate_condor_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.CHRISTMAS_TREE:
                strategies = await self._generate_christmas_tree_strategies(underlying, option_chain, spot_price)

            # Indian market specific buying strategies
            elif strategy_type == StrategyType.WEEKLY_EXPIRY_STRADDLE:
                strategies = await self._generate_weekly_expiry_straddle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.MONTHLY_EXPIRY_STRANGLE:
                strategies = await self._generate_monthly_expiry_strangle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.BANKNIFTY_BUTTERFLY:
                strategies = await self._generate_banknifty_butterfly_strategies(underlying, option_chain, spot_price)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate {strategy_type.value} strategies: {e}")
            return []
    
    async def _generate_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                           spot_price: float) -> List[OptionsStrategy]:
        """Generate long call strategies"""
        try:
            strategies = []
            
            # Get call options
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            
            for call_row in calls.iter_rows(named=True):
                # Create single leg strategy
                leg = OptionsLeg(
                    symbol=call_row['symbol'],
                    option_type='CE',
                    strike_price=call_row['strike_price'],
                    expiry_date='2024-01-25',  # Sample expiry
                    quantity=1,
                    premium=call_row['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                max_profit = float('inf')  # Unlimited upside
                max_loss = call_row['ltp']
                break_even = call_row['strike_price'] + call_row['ltp']
                
                # Estimate probability of profit (simplified)
                prob_profit = max(0.0, min(1.0, (spot_price - break_even) / spot_price + 0.5))
                
                strategy = OptionsStrategy(
                    strategy_id=f"LC_{underlying}_{call_row['strike_price']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_row['ltp'],  # Negative because we pay premium
                    margin_required=call_row['ltp'],
                    risk_reward_ratio=float('inf') if max_loss > 0 else 0,
                    target_profit=max_loss * 2,  # 200% return target
                    stop_loss=max_loss * 0.5,  # 50% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Price", "operator": "break_above", "value": spot_price * 1.01}],
                    exit_conditions=[{"indicator": "Price", "operator": "fall_below", "value": spot_price * 0.99}]
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long call strategies: {e}")
            return []
    
    async def _generate_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                          spot_price: float) -> List[OptionsStrategy]:
        """Generate long put strategies"""
        try:
            strategies = []
            
            # Get put options
            puts = option_chain.filter(pl.col('option_type') == 'PE')
            
            for put_row in puts.iter_rows(named=True):
                # Create single leg strategy
                leg = OptionsLeg(
                    symbol=put_row['symbol'],
                    option_type='PE',
                    strike_price=put_row['strike_price'],
                    expiry_date='2024-01-25',  # Sample expiry
                    quantity=1,
                    premium=put_row['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                max_profit = put_row['strike_price'] - put_row['ltp']
                max_loss = put_row['ltp']
                break_even = put_row['strike_price'] - put_row['ltp']
                
                # Estimate probability of profit
                prob_profit = max(0.0, min(1.0, (break_even - spot_price) / spot_price + 0.5))
                
                strategy = OptionsStrategy(
                    strategy_id=f"LP_{underlying}_{put_row['strike_price']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_row['ltp'],
                    margin_required=put_row['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "RSI", "operator": "<", "value": 30}], # Placeholder
                    exit_conditions=[{"indicator": "RSI", "operator": ">", "value": 70}] # Placeholder
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long put strategies: {e}")
            return []
    
    async def _generate_long_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate long straddle strategies"""
        try:
            strategies = []
            
            # Get unique strikes
            strikes = option_chain['strike_price'].unique().sort().to_list()
            
            for strike in strikes:
                # Get call and put at same strike
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') & 
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') & 
                    (pl.col('strike_price') == strike)
                ).height > 0 else None
                
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') & 
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') & 
                    (pl.col('strike_price') == strike)
                ).height > 0 else None
                
                if not call or not put:
                    continue
                
                # Create legs
                call_leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=call['ltp'],
                    underlying=underlying
                )
                
                put_leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=put['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                total_premium = call['ltp'] + put['ltp']
                max_profit = float('inf')  # Unlimited if big move
                max_loss = total_premium
                break_even_upper = strike + total_premium
                break_even_lower = strike - total_premium
                
                # Estimate probability of profit (move beyond break-evens)
                prob_profit = 0.4  # Simplified estimate for volatility strategy
                
                strategy = OptionsStrategy(
                    strategy_id=f"LS_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_STRADDLE,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=-total_premium,
                    margin_required=total_premium,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "IV", "operator": "low"}], # Placeholder
                    exit_conditions=[{"indicator": "IV", "operator": "high"}] # Placeholder
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long straddle strategies: {e}")
            return []
    
    async def _generate_long_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate long strangle strategies"""
        try:
            strategies = []

            # Get OTM strikes for calls and puts
            otm_call_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'CE')
            otm_put_strikes = self.calculators.get_otm_strikes(option_chain, spot_price, 'PE')

            for call_strike in otm_call_strikes[:3]:  # Limit to top 3
                for put_strike in otm_put_strikes[:3]:
                    if call_strike <= put_strike:
                        continue

                    # Get call and put options
                    call = option_chain.filter(
                        (pl.col('option_type') == 'CE') &
                        (pl.col('strike_price') == call_strike)
                    ).row(0, named=True) if option_chain.filter(
                        (pl.col('option_type') == 'CE') &
                        (pl.col('strike_price') == call_strike)
                    ).height > 0 else None

                    put = option_chain.filter(
                        (pl.col('option_type') == 'PE') &
                        (pl.col('strike_price') == put_strike)
                    ).row(0, named=True) if option_chain.filter(
                        (pl.col('option_type') == 'PE') &
                        (pl.col('strike_price') == put_strike)
                    ).height > 0 else None

                    if not call or not put:
                        continue

                    # Create legs
                    call_leg = OptionsLeg(
                        symbol=call['symbol'],
                        option_type='CE',
                        strike_price=call_strike,
                        expiry_date='2024-01-25',
                        quantity=1,
                        premium=call['ltp'],
                        underlying=underlying
                    )

                    put_leg = OptionsLeg(
                        symbol=put['symbol'],
                        option_type='PE',
                        strike_price=put_strike,
                        expiry_date='2024-01-25',
                        quantity=1,
                        premium=put['ltp'],
                        underlying=underlying
                    )

                    # Calculate strategy metrics
                    total_premium = call['ltp'] + put['ltp']
                    max_profit = float('inf')  # Unlimited if big move
                    max_loss = total_premium
                    break_even_upper = call_strike + total_premium
                    break_even_lower = put_strike - total_premium

                    # Estimate probability of profit
                    prob_profit = 0.35  # Lower than straddle due to wider strikes

                    strategy = OptionsStrategy(
                        strategy_id=f"LST_{underlying}_{call_strike}_{put_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                        strategy_type=StrategyType.LONG_STRANGLE,
                        underlying=underlying,
                        legs=[call_leg, put_leg],
                        max_profit=max_profit,
                        max_loss=max_loss,
                        break_even_points=[break_even_lower, break_even_upper],
                        probability_of_profit=prob_profit,
                        net_premium=-total_premium,
                        margin_required=total_premium,
                        risk_reward_ratio=float('inf'),
                        target_profit=max_loss * 2,
                        stop_loss=max_loss * 0.5,
                        created_at=datetime.now(),
                        entry_conditions=[{"indicator": "IV", "operator": "low"}], # Placeholder
                        exit_conditions=[{"indicator": "IV", "operator": "high"}] # Placeholder
                    )

                    strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long strangle strategies: {e}")
            return []
    
    # ATM Strategy Methods
    async def _generate_atm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long call strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not call:
                    continue

                leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=call['ltp'],
                    underlying=underlying
                )

                max_profit = float('inf')
                max_loss = call['ltp']
                break_even = strike + call['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call['ltp'],
                    margin_required=call['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": ">", "value": 10000}], # Placeholder
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM long call strategies: {e}")
            return []

    async def _generate_atm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                              spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long put strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not put:
                    continue

                leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=put['ltp'],
                    underlying=underlying
                )

                max_profit = strike - put['ltp']
                max_loss = put['ltp']
                break_even = strike - put['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put['ltp'],
                    margin_required=put['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": ">", "value": 10000}], # Placeholder
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM long put strategies: {e}")
            return []

    async def _generate_atm_short_call_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM short call strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not call:
                    continue

                leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=-1,  # Short position
                    premium=call['ltp'],
                    underlying=underlying
                )

                max_profit = call['ltp']
                max_loss = float('inf')  # Unlimited loss
                break_even = strike + call['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMSC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_SHORT_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=call['ltp'],  # Positive because we receive premium
                    margin_required=call['ltp'] * 10,  # Estimated margin
                    risk_reward_ratio=0,  # Unlimited loss
                    target_profit=max_profit * 0.8,  # 80% of max profit
                    stop_loss=max_profit * 2,  # 200% of premium received
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Delta", "operator": "near_zero"}], # Placeholder
                    exit_conditions=[{"indicator": "Delta", "operator": "far_from_zero"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM short call strategies: {e}")
            return []

    async def _generate_atm_short_put_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM short put strategies"""
        try:
            strategies = []
            atm_strikes = self.calculators.get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not put:
                    continue

                leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=-1,  # Short position
                    premium=put['ltp'],
                    underlying=underlying
                )

                max_profit = put['ltp']
                max_loss = strike - put['ltp']
                break_even = strike - put['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMSP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_SHORT_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=put['ltp'],  # Positive because we receive premium
                    margin_required=put['ltp'] * 10,  # Estimated margin
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,  # 80% of max profit
                    stop_loss=max_profit * 2,  # 200% of premium received
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Delta", "operator": "near_zero"}], # Placeholder
                    exit_conditions=[{"indicator": "Delta", "operator": "far_from_zero"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM short put strategies: {e}")
            return []

    async def _generate_bull_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                  spot_price: float) -> List[OptionsStrategy]:
        """Generate bull call spread strategies"""
        # Buy lower strike call, sell higher strike call
        # Implementation would create two-leg spread strategies
        return []
    
    async def _generate_bear_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                 spot_price: float) -> List[OptionsStrategy]:
        """Generate bear put spread strategies"""
        # Buy higher strike put, sell lower strike put
        # Implementation would create two-leg spread strategies
        return []

    async def _generate_iron_condor_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                             spot_price: float) -> List[OptionsStrategy]:
        """Generate iron condor strategies"""
        # Four-leg strategy: sell call spread + sell put spread
        # Implementation would create four-leg strategies
        return []

    # Placeholder methods for remaining strategies
    async def _generate_protective_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate protective put strategies"""
        return []

    async def _generate_atm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long call strategies"""
        return []

    async def _generate_atm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long put strategies"""
        return []

    async def _generate_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM long call strategies"""
        return []

    async def _generate_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM long put strategies"""
        return []

    async def _generate_far_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Far OTM long call strategies"""
        return []

    async def _generate_far_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Far OTM long put strategies"""
        return []

    async def _generate_intraday_scalping_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate intraday scalping call strategies"""
        return []

    async def _generate_intraday_scalping_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate intraday scalping put strategies"""
        return []

    async def _generate_intraday_momentum_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate intraday momentum call strategies"""
        return []

    async def _generate_intraday_momentum_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate intraday momentum put strategies"""
        return []

    async def _generate_intraday_reversal_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate intraday reversal call strategies"""
        return []

    async def _generate_intraday_reversal_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate intraday reversal put strategies"""
        return []

    async def _generate_gamma_scalping_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate gamma scalping long strategies"""
        return []

    async def _generate_delta_neutral_gamma_scalp_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate delta neutral gamma scalp strategies"""
        return []

    async def _generate_volatility_breakout_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate volatility breakout long strategies"""
        return []

    async def _generate_vix_based_strategy_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate VIX based strategy strategies"""
        return []

    async def _generate_reverse_iron_condor_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate reverse iron condor strategies (net debit)"""
        return []

    async def _generate_reverse_iron_butterfly_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate reverse iron butterfly strategies (net debit)"""
        return []

    async def _generate_bull_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate bull call spread strategies (net debit)"""
        return []
    
    async def _generate_bear_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate bear put spread strategies (net debit)"""
        return []

    async def _generate_call_calendar_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate call calendar spread strategies (net debit)"""
        return []

    async def _generate_put_calendar_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate put calendar spread strategies (net debit)"""
        return []

    async def _generate_diagonal_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate diagonal call spread strategies (net debit)"""
        return []

    async def _generate_diagonal_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate diagonal put spread strategies (net debit)"""
        return []

    async def _generate_ratio_call_backspread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ratio call backspread strategies (net debit)"""
        return []

    async def _generate_ratio_put_backspread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ratio put backspread strategies (net debit)"""
        return []

    async def _generate_collar_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate collar strategies (often net zero or small debit)"""
        return []

    async def _generate_synthetic_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long strategies"""
        return []

    async def _generate_synthetic_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long call strategies"""
        return []

    async def _generate_synthetic_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long put strategies"""
        return []

    async def _generate_butterfly_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate butterfly spread strategies (net debit)"""
        return []

    async def _generate_condor_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate condor spread strategies (net debit)"""
        return []

    async def _generate_christmas_tree_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate christmas tree strategies (net debit)"""
        return []

    async def _generate_weekly_expiry_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate weekly expiry straddle strategies"""
        return []

    async def _generate_monthly_expiry_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate monthly expiry strangle strategies"""
        return []

    async def _generate_banknifty_butterfly_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate BankNifty butterfly strategies (assuming net debit)"""
        return []
