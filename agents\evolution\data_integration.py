#!/usr/bin/env python3
"""
Data Integration Manager for Options Strategy Evolution

This module efficiently manages data loading and backtesting integration for strategy evolution.
It uses existing data from data/features/ directories and integrates directly with the backtesting agent
instead of downloading data from scratch.
"""

import asyncio
import logging
import polars as pl
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class BacktestConfig:
    """Configuration for backtesting"""
    timeframe: str = "5min"
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    symbols: List[str] = None
    max_trades: int = 1000
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ['NIFTY', 'BANKNIFTY']
        if self.start_date is None:
            self.start_date = datetime.now() - timedelta(days=30)
        if self.end_date is None:
            self.end_date = datetime.now()

class DataIntegrationManager:
    """Manages efficient data loading and backtesting for strategy evolution"""
    
    def __init__(self):
        self.data_path = Path("data")
        self.features_path = self.data_path / "features"
        self.cached_data: Dict[str, pl.DataFrame] = {}
        self.backtesting_agent = None
        
    async def initialize(self):
        """Initialize the data integration manager"""
        try:
            # Import and initialize backtesting agent
            from agents.backtesting_agent import OptionsBacktestingAgent
            self.backtesting_agent = OptionsBacktestingAgent()
            await self.backtesting_agent.initialize()
            
            logger.info("[DATA] Data integration manager initialized")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize data integration manager: {e}")
            return False
    
    async def load_features_data(self, timeframe: str = "5min", symbols: List[str] = None) -> Optional[pl.DataFrame]:
        """Load features data from existing data/features/ directory"""
        try:
            if symbols is None:
                symbols = ['NIFTY', 'BANKNIFTY']
            
            cache_key = f"{timeframe}_{'-'.join(symbols)}"
            if cache_key in self.cached_data:
                logger.debug(f"[DATA] Using cached data for {cache_key}")
                return self.cached_data[cache_key]
            
            features_timeframe_path = self.features_path / timeframe
            if not features_timeframe_path.exists():
                logger.warning(f"[DATA] Features path not found: {features_timeframe_path}")
                return None
            
            # Load all parquet files for the timeframe
            parquet_files = list(features_timeframe_path.glob("*.parquet"))
            if not parquet_files:
                logger.warning(f"[DATA] No parquet files found in {features_timeframe_path}")
                return None
            
            # Load and combine data
            dataframes = []
            for file in parquet_files:
                try:
                    df = pl.read_parquet(file)
                    
                    # Check if file contains data for requested symbols based on filename
                    file_contains_symbol = any(symbol in file.name for symbol in symbols)
                    if not file_contains_symbol:
                        continue
                    
                    # Filter for requested symbols if symbol column exists
                    if 'symbol' in df.columns:
                        df = df.filter(pl.col('symbol').is_in(symbols))
                    elif 'underlying' in df.columns:
                        df = df.filter(pl.col('underlying').is_in(symbols))
                    
                    if df.height > 0:
                        dataframes.append(df)
                        logger.debug(f"[DATA] Loaded {df.height} rows from {file.name}")
                        
                except Exception as e:
                    logger.warning(f"[DATA] Failed to load {file}: {e}")
                    continue
            
            if not dataframes:
                logger.warning(f"[DATA] No valid data loaded for {timeframe}. Found {len(parquet_files)} files, symbols: {symbols}")
                # Log file names for debugging
                for file in parquet_files[:5]:  # Show first 5 files
                    logger.debug(f"[DATA] Available file: {file.name}")
                
                # Fallback: try loading all files without symbol filtering
                logger.info(f"[DATA] Attempting fallback: loading all files for {timeframe}")
                for file in parquet_files:
                    try:
                        df = pl.read_parquet(file)
                        if df.height > 0:
                            dataframes.append(df)
                            logger.debug(f"[DATA] Fallback loaded {df.height} rows from {file.name}")
                    except Exception as e:
                        logger.warning(f"[DATA] Fallback failed to load {file}: {e}")
                        continue
                
                if not dataframes:
                    return None
            
            # Combine dataframes with flexible column handling
            if dataframes:
                # Find common columns across all dataframes
                all_columns = [set(df.columns) for df in dataframes]
                common_columns = set.intersection(*all_columns) if all_columns else set()
                
                if not common_columns:
                    logger.warning(f"[DATA] No common columns found across dataframes for {timeframe}")
                    return None
                
                # Select only common columns and harmonize data types
                aligned_dataframes = []
                for df in dataframes:
                    df_aligned = df.select(sorted(common_columns))
                    
                    # Harmonize data types for common numeric columns
                    for col in df_aligned.columns:
                        if col in ['strike_price', 'open', 'high', 'low', 'close', 'volume']:
                            df_aligned = df_aligned.with_columns(pl.col(col).cast(pl.Float64, strict=False))
                        elif col in ['hour', 'weekday', 'month']:
                            df_aligned = df_aligned.with_columns(pl.col(col).cast(pl.Int32, strict=False))
                        elif col in ['underlying', 'option_type', 'symbol', 'strategy_prefix', 'strategy_id']:
                            df_aligned = df_aligned.with_columns(pl.col(col).cast(pl.Utf8, strict=False))
                    
                    aligned_dataframes.append(df_aligned)
                    logger.debug(f"[DATA] Using {len(common_columns)} common columns from {df.height} rows")

                # Combine all aligned dataframes with diagonal concat to handle any remaining type issues
                combined_df = pl.concat(aligned_dataframes, how="diagonal")

                # Sort by timestamp if available
                if 'timestamp' in combined_df.columns:
                    combined_df = combined_df.sort('timestamp')
            else:
                logger.warning(f"[DATA] No dataframes to combine for {timeframe}")
                return None
            
            # Cache the data
            self.cached_data[cache_key] = combined_df
            
            logger.info(f"[DATA] Loaded {combined_df.height} rows of features data for {timeframe}")
            return combined_df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load features data: {e}")
            return None
    
    async def run_strategy_backtest(self, strategy_config: Dict, config: BacktestConfig) -> Optional[Dict]:
        """Run backtest using existing backtesting agent with loaded data"""
        try:
            if not self.backtesting_agent:
                raise ValueError("Backtesting agent not initialized")

            # Load features data for the specified timeframe
            features_data = await self.load_features_data(config.timeframe, config.symbols)
            if features_data is None:
                raise ValueError(f"No features data available for {config.timeframe}")

            # Filter data by date range
            if 'timestamp' in features_data.columns:
                # Ensure timezone compatibility for comparison
                start_date = config.start_date
                end_date = config.end_date

                # If the timestamp column has timezone info but our dates don't, add UTC timezone
                if start_date.tzinfo is None:
                    import pytz
                    start_date = pytz.UTC.localize(start_date)
                    end_date = pytz.UTC.localize(end_date)

                features_data = features_data.filter(
                    (pl.col('timestamp') >= start_date) &
                    (pl.col('timestamp') <= end_date)
                )

            if features_data.height == 0:
                raise ValueError("No data available for specified date range")

            # Convert strategy config to the format expected by backtesting agent
            backtest_strategy = self._convert_strategy_config(strategy_config)

            # Prepare market data in the format expected by backtesting agent
            market_data = self._prepare_market_data_for_backtest(features_data, config)

            # Run backtest using the backtesting agent directly with optimized parameters
            backtest_results = await self._run_optimized_backtest(
                backtest_strategy, market_data, config
            )

            if not backtest_results:
                raise ValueError("Backtesting failed to produce results")

            logger.debug(f"[BACKTEST] Completed backtest for strategy {strategy_config.get('strategy_id', 'unknown')}")
            return backtest_results

        except Exception as e:
            logger.error(f"[ERROR] Strategy backtest failed: {e}")
            return None

    async def _run_optimized_backtest(self, strategy, market_data: Dict, config: BacktestConfig) -> Optional[Dict]:
        """Run real backtest using the backtesting agent's engine directly"""
        try:
            # Use the backtesting agent's engine directly with real data
            if not self.backtesting_agent or not self.backtesting_agent.engine:
                raise ValueError("Backtesting engine not available")
            
            # Convert market data to format expected by backtesting engine
            import polars as pl
            feature_data = pl.DataFrame(market_data)
            
            # Convert StrategyConfig to dictionary format expected by engine
            strategy_dict = strategy.to_dict() if hasattr(strategy, 'to_dict') else strategy
            strategies = [strategy_dict]
            
            # Run backtest using the engine directly
            backtest_results = self.backtesting_agent.engine.run_backtest(
                feature_data, strategies, self.backtesting_agent.config
            )

            if not backtest_results:
                return {'trades': [], 'error': 'No backtest results generated'}

            # Extract results for this strategy
            strategy_id = strategy.strategy_id if hasattr(strategy, 'strategy_id') else 'unknown'

            # VectorBT engine returns results with strategy IDs as direct keys
            if strategy_id in backtest_results:
                strategy_results = backtest_results[strategy_id]
                # Return the BacktestResults object directly (not as dict)
                return strategy_results
            else:
                # Fallback: check if results are nested under 'strategy_results' key
                if 'strategy_results' in backtest_results:
                    strategy_results = backtest_results['strategy_results'].get(strategy_id, {})
                    if strategy_results:
                        return strategy_results

                return {'trades': [], 'error': f'No results found for strategy {strategy_id}'}

        except Exception as e:
            logger.error(f"[ERROR] Real backtest failed: {e}")
            return {'trades': [], 'error': str(e)}

    def _generate_parameter_variations(self, strategy, config: BacktestConfig) -> List:
        """Generate parameter variations for testing"""
        try:
            variations = [strategy]  # Include original

            # Create variations based on strategy type and timeframe
            if hasattr(strategy, 'parameters') and strategy.parameters:
                # Variation 1: Adjust risk parameters
                risk_variation = self._copy_strategy_with_risk_adjustment(strategy, 0.8)
                if risk_variation:
                    variations.append(risk_variation)

                # Variation 2: Adjust timeframe if different timeframes available
                if config.timeframe != '5min':
                    timeframe_variation = self._copy_strategy_with_timeframe(strategy, '5min')
                    if timeframe_variation:
                        variations.append(timeframe_variation)

                # Variation 3: Adjust entry conditions sensitivity
                sensitivity_variation = self._copy_strategy_with_sensitivity_adjustment(strategy, 1.2)
                if sensitivity_variation:
                    variations.append(sensitivity_variation)

            return variations[:3]  # Limit to 3 variations for performance

        except Exception as e:
            logger.debug(f"[BACKTEST] Parameter variation generation failed: {e}")
            return [strategy]

    def _copy_strategy_with_risk_adjustment(self, strategy, risk_factor: float):
        """Create strategy copy with adjusted risk parameters"""
        try:
            # This is a simplified version - in practice, you'd use the actual strategy copying logic
            return strategy  # Return original for now
        except Exception:
            return None

    def _copy_strategy_with_timeframe(self, strategy, new_timeframe: str):
        """Create strategy copy with different timeframe"""
        try:
            # This is a simplified version - in practice, you'd use the actual strategy copying logic
            return strategy  # Return original for now
        except Exception:
            return None

    def _copy_strategy_with_sensitivity_adjustment(self, strategy, sensitivity_factor: float):
        """Create strategy copy with adjusted sensitivity"""
        try:
            # This is a simplified version - in practice, you'd use the actual strategy copying logic
            return strategy  # Return original for now
        except Exception:
            return None

    def _calculate_quick_score(self, returns: List[float]) -> float:
        """Calculate quick performance score for parameter variation selection"""
        try:
            if not returns:
                return -100.0

            import numpy as np
            returns_array = np.array(returns)

            total_return = np.sum(returns_array)
            win_rate = np.sum(returns_array > 0) / len(returns_array)
            volatility = np.std(returns_array)

            # Simple composite score
            score = total_return * 0.4 + win_rate * 50 - volatility * 20
            return float(score)

        except Exception as e:
            logger.debug(f"[BACKTEST] Quick score calculation failed: {e}")
            return -100.0

    def _prepare_market_data_for_backtest(self, features_data: pl.DataFrame, config: BacktestConfig) -> pl.DataFrame:
        """Prepare market data in the format expected by backtesting agent"""
        try:
            # Return the Polars DataFrame directly for the backtesting engine
            return features_data

        except Exception as e:
            logger.error(f"[ERROR] Market data preparation failed: {e}")
            return pl.DataFrame()
    
    def _convert_strategy_config(self, strategy_config: Dict) -> Any:
        """Convert strategy config to format expected by backtesting agent"""
        try:
            # Import the StrategyConfig class from the correct module
            from agents.evolution.strategy_management import StrategyConfig, StrategyStatus
            
            # Create StrategyConfig object
            return StrategyConfig(
                strategy_id=strategy_config.get('strategy_id', 'test_strategy'),
                name=strategy_config.get('name', 'Test Strategy'),
                description=strategy_config.get('description', 'Strategy for backtesting'),
                parameters=strategy_config.get('parameters', {}),
                entry_conditions=strategy_config.get('entry_conditions', []),
                exit_conditions=strategy_config.get('exit_conditions', []),
                risk_management=strategy_config.get('risk_management', {}),
                market_outlook=strategy_config.get('market_outlook', 'neutral'),
                volatility_outlook=strategy_config.get('volatility_outlook', 'normal'),
                timeframe=strategy_config.get('timeframe', '5min'),
                status=StrategyStatus.EXPERIMENTAL
            )
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to convert strategy config: {e}")
            return strategy_config
    
    async def get_available_timeframes(self) -> List[str]:
        """Get list of available timeframes from features data"""
        try:
            if not self.features_path.exists():
                return []
            
            timeframes = []
            for item in self.features_path.iterdir():
                if item.is_dir() and any(item.glob("*.parquet")):
                    timeframes.append(item.name)
            
            return sorted(timeframes)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get available timeframes: {e}")
            return []
    
    async def get_data_summary(self, timeframe: str = "5min") -> Dict[str, Any]:
        """Get summary of available data for a timeframe"""
        try:
            features_data = await self.load_features_data(timeframe)
            if features_data is None:
                return {}
            
            summary = {
                'timeframe': timeframe,
                'total_rows': features_data.height,
                'columns': features_data.columns,
                'date_range': {}
            }
            
            if 'timestamp' in features_data.columns:
                timestamps = features_data.select('timestamp').to_series()
                summary['date_range'] = {
                    'start': timestamps.min(),
                    'end': timestamps.max()
                }
            
            if 'symbol' in features_data.columns:
                symbols = features_data.select('symbol').unique().to_series().to_list()
                summary['symbols'] = symbols
            elif 'underlying' in features_data.columns:
                symbols = features_data.select('underlying').unique().to_series().to_list()
                summary['symbols'] = symbols
            
            return summary
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get data summary: {e}")
            return {}
    
    def clear_cache(self):
        """Clear cached data"""
        self.cached_data.clear()
        logger.info("[DATA] Data cache cleared")
