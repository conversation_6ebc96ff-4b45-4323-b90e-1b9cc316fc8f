#!/usr/bin/env python3
"""
Evolution Core Module for Options Strategy Evolution

This module orchestrates the main evolution process, coordinating all other modules
to provide a comprehensive strategy evolution system.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from concurrent.futures import ThreadPoolExecutor
import random

from .data_integration import DataIntegrationManager, BacktestConfig
from .performance_analysis import PerformanceAnalyzer, PerformanceMetrics
from .genetic_operations import GeneticOperations
from .strategy_management import StrategyManager, StrategyConfig, StrategyStatus

logger = logging.getLogger(__name__)

class EvolutionCore:
    """Core evolution orchestrator that coordinates all evolution modules"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._get_default_config()
        self.is_running = False
        
        # Initialize thread pool for parallel processing
        self.thread_pool = ThreadPoolExecutor(max_workers=self.config.get('max_workers', 8))
        
        # Initialize modules
        self.data_manager = DataIntegrationManager()
        self.performance_analyzer = PerformanceAnalyzer(self.thread_pool)
        self.genetic_ops = GeneticOperations(self.performance_analyzer, self.thread_pool)
        self.strategy_manager = StrategyManager(self.performance_analyzer)
        
        # Evolution parameters
        self.population_size = self.config.get('population_size', 100)
        self.mutation_rate = self.config.get('mutation_rate', 0.15)
        self.crossover_rate = self.config.get('crossover_rate', 0.8)
        self.selection_pressure = self.config.get('selection_pressure', 0.3)
        
        # Evolution intervals
        self.evolution_interval = self.config.get('evolution_interval', 3600)  # 1 hour
        self.performance_check_interval = self.config.get('performance_check_interval', 300)  # 5 minutes
        
        # Performance tracking
        self.last_evolution_time = 0
        self.last_performance_check = 0
        self.evolution_cycle_count = 0
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'population_size': 100,
            'mutation_rate': 0.15,
            'crossover_rate': 0.8,
            'selection_pressure': 0.3,
            'evolution_interval': 3600,
            'performance_check_interval': 300,
            'max_workers': 8,
            'timeframes': ['5min', '15min'],
            'symbols': ['NIFTY', 'BANKNIFTY'],
            'backtest_days': 30
        }
    
    async def initialize(self) -> bool:
        """Initialize the evolution core and all modules"""
        try:
            logger.info("[EVOLUTION] Initializing evolution core...")
            
            # Initialize all modules
            modules_initialized = await asyncio.gather(
                self.data_manager.initialize(),
                self.strategy_manager.initialize(),
                return_exceptions=True
            )
            
            if not all(result is True for result in modules_initialized if not isinstance(result, Exception)):
                raise RuntimeError("Failed to initialize one or more modules")
            
            # Set population size in strategy manager
            self.strategy_manager.population_size = self.population_size
            
            logger.info("[EVOLUTION] Evolution core initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Evolution core initialization failed: {e}")
            return False
    
    async def start_evolution_loop(self):
        """Start the main evolution loop"""
        try:
            if self.is_running:
                logger.warning("[EVOLUTION] Evolution loop is already running")
                return
            
            self.is_running = True
            logger.info("[EVOLUTION] Starting evolution loop...")
            
            while self.is_running:
                try:
                    current_time = datetime.now().timestamp()
                    
                    # Performance check
                    if current_time - self.last_performance_check >= self.performance_check_interval:
                        await self._perform_performance_check()
                        self.last_performance_check = current_time
                    
                    # Evolution cycle
                    if current_time - self.last_evolution_time >= self.evolution_interval:
                        await self._perform_evolution_cycle()
                        self.last_evolution_time = current_time
                        self.evolution_cycle_count += 1
                    
                    # Sleep for a short interval
                    await asyncio.sleep(60)  # Check every minute
                    
                except Exception as e:
                    logger.error(f"[ERROR] Evolution loop iteration failed: {e}")
                    await asyncio.sleep(300)  # Wait 5 minutes before retrying
            
        except Exception as e:
            logger.error(f"[ERROR] Evolution loop failed: {e}")
        finally:
            self.is_running = False
            logger.info("[EVOLUTION] Evolution loop stopped")
    
    async def stop_evolution_loop(self):
        """Stop the evolution loop"""
        self.is_running = False
        logger.info("[EVOLUTION] Stopping evolution loop...")
    
    async def _perform_performance_check(self):
        """Perform regular performance check on all strategies"""
        try:
            logger.debug("[EVOLUTION] Performing performance check...")
            
            # Get all strategies
            all_strategies = list(self.strategy_manager.strategy_registry.values())
            
            if not all_strategies:
                logger.info("[EVOLUTION] No strategies found for performance check")
                return
            
            # Check performance for strategies that need evaluation
            strategies_to_evaluate = []
            for strategy in all_strategies:
                # Check if strategy needs performance evaluation
                if strategy.strategy_id not in self.performance_analyzer.performance_cache:
                    strategies_to_evaluate.append(strategy)
                else:
                    # Check if cached performance is old (> 1 hour)
                    cached_metrics = self.performance_analyzer.performance_cache[strategy.strategy_id]
                    if (datetime.now() - cached_metrics.timestamp).seconds > 3600:
                        strategies_to_evaluate.append(strategy)
            
            if strategies_to_evaluate:
                logger.info(f"[EVOLUTION] Evaluating performance for {len(strategies_to_evaluate)} strategies")
                await self._evaluate_strategies_performance(strategies_to_evaluate)
            
        except Exception as e:
            logger.error(f"[ERROR] Performance check failed: {e}")
    
    async def _perform_evolution_cycle(self):
        """Perform a complete evolution cycle"""
        try:
            logger.info(f"[EVOLUTION] Starting evolution cycle #{self.evolution_cycle_count + 1}")
            
            # Get current population
            current_strategies = list(self.strategy_manager.strategy_registry.values())
            
            if len(current_strategies) < 2:
                logger.warning("[EVOLUTION] Not enough strategies for evolution, need at least 2")
                return
            
            # Step 1: Evaluate all strategies
            await self._evaluate_strategies_performance(current_strategies)
            
            # Step 2: Select top performers
            strategy_dicts = [strategy.to_dict() for strategy in current_strategies]
            top_performers = await self.genetic_ops.select_top_performers(
                strategy_dicts, self.selection_pressure
            )
            
            if len(top_performers) < 2:
                logger.warning("[EVOLUTION] Not enough top performers for crossover")
                return
            
            # Step 3: Create offspring through crossover
            max_offspring = min(50, len(top_performers) * 2)
            offspring = await self.genetic_ops.create_crossover_strategies(top_performers, max_offspring)
            
            # Step 4: Apply mutations
            mutated_offspring = []
            for child in offspring:
                if random.random() < self.mutation_rate:
                    mutation_type = random.choice(['adaptive', 'conservative', 'aggressive'])
                    mutated_child = await self.genetic_ops.apply_mutations(child, mutation_type)
                    mutated_offspring.append(mutated_child)
                else:
                    mutated_offspring.append(child)
            
            # Step 5: Add new strategies to registry
            added_count = 0
            for child_dict in mutated_offspring:
                try:
                    child_strategy = StrategyConfig.from_dict(child_dict)
                    child_strategy.status = StrategyStatus.EXPERIMENTAL
                    
                    if await self.strategy_manager.add_strategy(child_strategy):
                        added_count += 1
                        
                except Exception as e:
                    logger.debug(f"[EVOLUTION] Failed to add offspring strategy: {e}")
                    continue
            
            # Step 6: Control population size
            await self.strategy_manager.control_population_size()
            
            # Step 7: Export evolved strategies
            await self.strategy_manager.export_strategies_to_yaml()
            
            logger.info(f"[EVOLUTION] Evolution cycle completed: added {added_count} new strategies")
            
        except Exception as e:
            logger.error(f"[ERROR] Evolution cycle failed: {e}")
    
    async def _evaluate_strategies_performance(self, strategies: List[StrategyConfig]):
        """Evaluate performance for a list of strategies"""
        try:
            if not strategies:
                return
            
            # Create backtest configuration
            backtest_config = BacktestConfig(
                timeframe=random.choice(self.config.get('timeframes', ['5min'])),
                symbols=self.config.get('symbols', ['NIFTY', 'BANKNIFTY']),
                start_date=datetime.now() - timedelta(days=self.config.get('backtest_days', 30)),
                end_date=datetime.now()
            )
            
            # Evaluate strategies in parallel
            evaluation_tasks = []
            for strategy in strategies:
                task = self._evaluate_single_strategy(strategy, backtest_config)
                evaluation_tasks.append(task)
            
            # Wait for all evaluations to complete
            results = await asyncio.gather(*evaluation_tasks, return_exceptions=True)
            
            successful_evaluations = sum(1 for result in results if not isinstance(result, Exception))
            logger.info(f"[EVOLUTION] Completed {successful_evaluations}/{len(strategies)} strategy evaluations")
            
        except Exception as e:
            logger.error(f"[ERROR] Strategy evaluation failed: {e}")
    
    async def _evaluate_single_strategy(self, strategy: StrategyConfig, config: BacktestConfig):
        """Evaluate a single strategy's performance"""
        try:
            # Run backtest
            backtest_results = await self.data_manager.run_strategy_backtest(
                strategy.to_dict(), config
            )
            
            if not backtest_results:
                raise ValueError(f"No backtest results for strategy {strategy.strategy_id}")
            
            # Analyze performance
            performance_metrics = await self.performance_analyzer.analyze_strategy_performance(
                strategy.to_dict(), backtest_results
            )
            
            logger.debug(f"[EVOLUTION] Evaluated {strategy.strategy_id}: score={performance_metrics.composite_score:.3f}")
            
        except Exception as e:
            logger.debug(f"[EVOLUTION] Failed to evaluate strategy {strategy.strategy_id}: {e}")
            raise
    
    async def trigger_evolution_cycle(self):
        """Manually trigger an evolution cycle"""
        try:
            logger.info("[EVOLUTION] Manually triggering evolution cycle...")
            await self._perform_evolution_cycle()
            
        except Exception as e:
            logger.error(f"[ERROR] Manual evolution cycle failed: {e}")
    
    async def get_evolution_status(self) -> Dict[str, Any]:
        """Get current evolution status"""
        try:
            registry_summary = self.strategy_manager.get_registry_summary()
            
            return {
                'is_running': self.is_running,
                'evolution_cycles_completed': self.evolution_cycle_count,
                'last_evolution_time': datetime.fromtimestamp(self.last_evolution_time).isoformat() if self.last_evolution_time else None,
                'last_performance_check': datetime.fromtimestamp(self.last_performance_check).isoformat() if self.last_performance_check else None,
                'registry_summary': registry_summary,
                'config': self.config
            }
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get evolution status: {e}")
            return {'error': str(e)}
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            await self.stop_evolution_loop()
            
            if hasattr(self, 'thread_pool'):
                self.thread_pool.shutdown(wait=True)
            
            logger.info("[EVOLUTION] Evolution core cleanup completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Evolution core cleanup failed: {e}")
