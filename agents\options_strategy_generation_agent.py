#!/usr/bin/env python3
"""
Options Strategy Generation Agent - Dynamic Strategy Creation & Optimization

Features:
📊 1. Strategy Generation
- Directional strategies (Long Call/Put, Covered Call)
- Volatility strategies (<PERSON>raddle, Strangle, Iron Condor)
- Spread strategies (Bull/Bear spreads, Calendar spreads)
- Complex multi-leg strategies

📈 2. Dynamic Optimization
- Real-time strategy parameter tuning
- Market regime-based strategy selection
- Risk-adjusted strategy optimization
- Greeks-based strategy construction

⚡ 3. Strategy Validation
- Risk-reward analysis
- Probability of profit calculations
- Maximum loss/profit scenarios
- Break-even point analysis

🎯 4. Performance Optimization
- Vectorized strategy calculations
- Polars + PyArrow for fast processing
- Parallel strategy evaluation
- Memory-efficient strategy storage
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import aiofiles
from dataclasses import dataclass, asdict
import json
from enum import Enum
import itertools

# Options pricing
try:
    import py_vollib.black_scholes as bs
    import py_vollib.black_scholes.greeks.analytical as greeks
except ImportError:
    logger.warning("py_vollib not installed. Using fallback calculations.")

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Comprehensive options strategy types for Indian market"""

    # Basic Directional Strategies
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"
    SHORT_CALL = "short_call"
    SHORT_PUT = "short_put"
    COVERED_CALL = "covered_call"
    PROTECTIVE_PUT = "protective_put"
    CASH_SECURED_PUT = "cash_secured_put"

    # ATM Strategies
    ATM_LONG_CALL = "atm_long_call"
    ATM_LONG_PUT = "atm_long_put"
    ATM_SHORT_CALL = "atm_short_call"
    ATM_SHORT_PUT = "atm_short_put"

    # OTM Strategies
    OTM_LONG_CALL = "otm_long_call"
    OTM_LONG_PUT = "otm_long_put"
    OTM_SHORT_CALL = "otm_short_call"
    OTM_SHORT_PUT = "otm_short_put"

    # Far OTM Strategies (Deep OTM)
    FAR_OTM_LONG_CALL = "far_otm_long_call"
    FAR_OTM_LONG_PUT = "far_otm_long_put"
    FAR_OTM_SHORT_CALL = "far_otm_short_call"
    FAR_OTM_SHORT_PUT = "far_otm_short_put"
    
    # Deep OTM Strategies (Very Far OTM)
    DEEP_OTM_LONG_CALL = "deep_otm_long_call"
    DEEP_OTM_LONG_PUT = "deep_otm_long_put"
    DEEP_OTM_SHORT_CALL = "deep_otm_short_call"
    DEEP_OTM_SHORT_PUT = "deep_otm_short_put"
    
    # Intraday Strategies
    INTRADAY_SCALPING_CALL = "intraday_scalping_call"
    INTRADAY_SCALPING_PUT = "intraday_scalping_put"
    INTRADAY_MOMENTUM_CALL = "intraday_momentum_call"
    INTRADAY_MOMENTUM_PUT = "intraday_momentum_put"
    INTRADAY_REVERSAL_CALL = "intraday_reversal_call"
    INTRADAY_REVERSAL_PUT = "intraday_reversal_put"
    
    # Gamma Scalping Strategies
    GAMMA_SCALPING_LONG = "gamma_scalping_long"
    GAMMA_SCALPING_SHORT = "gamma_scalping_short"
    DELTA_NEUTRAL_GAMMA_SCALP = "delta_neutral_gamma_scalp"
    
    # Volatility Breakout Strategies
    VOLATILITY_BREAKOUT_LONG = "volatility_breakout_long"
    VOLATILITY_BREAKOUT_SHORT = "volatility_breakout_short"
    VIX_BASED_STRATEGY = "vix_based_strategy"

    # Volatility Strategies
    LONG_STRADDLE = "long_straddle"
    SHORT_STRADDLE = "short_straddle"
    LONG_STRANGLE = "long_strangle"
    SHORT_STRANGLE = "short_strangle"
    IRON_CONDOR = "iron_condor"
    IRON_BUTTERFLY = "iron_butterfly"
    REVERSE_IRON_CONDOR = "reverse_iron_condor"
    REVERSE_IRON_BUTTERFLY = "reverse_iron_butterfly"

    # Spread Strategies
    BULL_CALL_SPREAD = "bull_call_spread"
    BEAR_CALL_SPREAD = "bear_call_spread"
    BULL_PUT_SPREAD = "bull_put_spread"
    BEAR_PUT_SPREAD = "bear_put_spread"
    CALL_CALENDAR_SPREAD = "call_calendar_spread"
    PUT_CALENDAR_SPREAD = "put_calendar_spread"
    DIAGONAL_CALL_SPREAD = "diagonal_call_spread"
    DIAGONAL_PUT_SPREAD = "diagonal_put_spread"

    # Ratio Strategies
    RATIO_CALL_SPREAD = "ratio_call_spread"
    RATIO_PUT_SPREAD = "ratio_put_spread"
    RATIO_CALL_BACKSPREAD = "ratio_call_backspread"
    RATIO_PUT_BACKSPREAD = "ratio_put_backspread"

    # Collar Strategies
    COLLAR = "collar"
    REVERSE_COLLAR = "reverse_collar"

    # Synthetic Strategies
    SYNTHETIC_LONG = "synthetic_long"
    SYNTHETIC_SHORT = "synthetic_short"
    SYNTHETIC_CALL = "synthetic_call"
    SYNTHETIC_PUT = "synthetic_put"

    # Complex Multi-leg Strategies
    JADE_LIZARD = "jade_lizard"
    REVERSE_JADE_LIZARD = "reverse_jade_lizard"
    BUTTERFLY_SPREAD = "butterfly_spread"
    CONDOR_SPREAD = "condor_spread"
    CHRISTMAS_TREE = "christmas_tree"

    # Indian Market Specific
    WEEKLY_EXPIRY_STRADDLE = "weekly_expiry_straddle"
    MONTHLY_EXPIRY_STRANGLE = "monthly_expiry_strangle"
    NIFTY_IRON_CONDOR = "nifty_iron_condor"
    BANKNIFTY_BUTTERFLY = "banknifty_butterfly"

@dataclass
class OptionsLeg:
    """Single options leg in a strategy"""
    symbol: str
    option_type: str  # CE or PE
    strike_price: float
    expiry_date: str
    quantity: int  # Positive for long, negative for short
    premium: float
    underlying: str

@dataclass
class OptionsStrategy:
    """Complete options strategy definition"""
    strategy_id: str
    strategy_type: StrategyType
    underlying: str
    legs: List[OptionsLeg]
    max_profit: float
    max_loss: float
    break_even_points: List[float]
    probability_of_profit: float
    net_premium: float
    margin_required: float
    risk_reward_ratio: float
    target_profit: float
    stop_loss: float
    created_at: datetime
    entry_conditions: List[Dict]
    exit_conditions: List[Dict]
    
    def to_dict(self) -> Dict:
        """Convert strategy to dictionary"""
        return {
            'strategy_id': self.strategy_id,
            'strategy_type': self.strategy_type.value,
            'underlying': self.underlying,
            'legs': [asdict(leg) for leg in self.legs],
            'max_profit': self.max_profit,
            'max_loss': self.max_loss,
            'break_even_points': self.break_even_points,
            'probability_of_profit': self.probability_of_profit,
            'net_premium': self.net_premium,
            'margin_required': self.margin_required,
            'risk_reward_ratio': self.risk_reward_ratio,
            'target_profit': self.target_profit,
            'stop_loss': self.stop_loss,
            'created_at': self.created_at.isoformat(),
            'entry_conditions': self.entry_conditions,
            'exit_conditions': self.exit_conditions
        }

class OptionsStrategyGenerationAgent:
    """
    Options Strategy Generation Agent for creating and optimizing options strategies

    IMPORTANT CLARIFICATION:
    This agent generates OPTIONS STRATEGIES (actual option combinations like straddles, spreads, etc.)
    This is different from SIGNAL GENERATION STRATEGIES in config/options_strategies.yaml which define
    WHEN to trade (entry conditions, RSI levels, etc.).

    This agent generates WHAT to trade:
    - Dynamic strategy generation based on market conditions
    - Strategy parameter optimization
    - Risk-reward analysis
    - Strategy validation and filtering
    - Multi-leg strategy construction

    The signal generation strategies from YAML are used by the signal generation agent
    to determine WHEN to execute these options strategies.
    """
    
    def __init__(self, config_path: str = "config/options_strategy_generation_config.yaml"):
        """Initialize Options Strategy Generation Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False
        
        # Data paths
        self.data_path = Path("data")
        self.features_path = self.data_path / "features"
        self.strategies_path = self.data_path / "strategies"
        self.option_chains_path = self.data_path / "option_chains"
        
        # Create directories
        self.strategies_path.mkdir(parents=True, exist_ok=True)
        
        # Strategy cache
        self.generated_strategies = {}
        self.market_data_cache = {}
        
        # Risk-free rate
        self.risk_free_rate = 0.06
        
        logger.info("[INIT] Options Strategy Generation Agent initialized")
    
    async def initialize(self, **kwargs):
        """Initialize the agent with optional parameters"""
        try:
            # Load configuration
            await self._load_config()
            
            # Store kwargs for later use
            self.init_kwargs = kwargs
            
            # Load market data
            await self._load_market_data()
            
            logger.info("[SUCCESS] Options Strategy Generation Agent initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False
    
    async def _load_config(self):
        """Load configuration from file"""
        try:
            # Default configuration with comprehensive strategy types
            self.config = {
                'underlying_symbols': ['NIFTY', 'BANKNIFTY'],
                'strategy_types': [
                    # Basic directional buying strategies
                    StrategyType.LONG_CALL,
                    StrategyType.LONG_PUT,
                    StrategyType.PROTECTIVE_PUT, # Often used for hedging a long stock position

                    # ATM buying strategies
                    StrategyType.ATM_LONG_CALL,
                    StrategyType.ATM_LONG_PUT,

                    # OTM buying strategies
                    StrategyType.OTM_LONG_CALL,
                    StrategyType.OTM_LONG_PUT,

                    # Far OTM buying strategies
                    StrategyType.FAR_OTM_LONG_CALL,
                    StrategyType.FAR_OTM_LONG_PUT,
                    
                    # Deep OTM buying strategies
                    StrategyType.DEEP_OTM_LONG_CALL,
                    StrategyType.DEEP_OTM_LONG_PUT,

                    # Intraday buying strategies
                    StrategyType.INTRADAY_SCALPING_CALL,
                    StrategyType.INTRADAY_SCALPING_PUT,
                    StrategyType.INTRADAY_MOMENTUM_CALL,
                    StrategyType.INTRADAY_MOMENTUM_PUT,
                    StrategyType.INTRADAY_REVERSAL_CALL,
                    StrategyType.INTRADAY_REVERSAL_PUT,
                    
                    # Gamma Scalping (Long)
                    StrategyType.GAMMA_SCALPING_LONG,
                    StrategyType.DELTA_NEUTRAL_GAMMA_SCALP,
                    
                    # Volatility Breakout (Long)
                    StrategyType.VOLATILITY_BREAKOUT_LONG,
                    StrategyType.VIX_BASED_STRATEGY,

                    # Volatility buying strategies
                    StrategyType.LONG_STRADDLE,
                    StrategyType.LONG_STRANGLE,
                    StrategyType.IRON_BUTTERFLY, # Can be net debit or credit, but often used for defined risk
                    StrategyType.REVERSE_IRON_CONDOR, # Net debit strategy
                    StrategyType.REVERSE_IRON_BUTTERFLY, # Net debit strategy

                    # Spread buying strategies (net debit spreads)
                    StrategyType.BULL_CALL_SPREAD, # Debit spread
                    StrategyType.BEAR_PUT_SPREAD,  # Debit spread
                    StrategyType.CALL_CALENDAR_SPREAD, # Debit spread
                    StrategyType.PUT_CALENDAR_SPREAD,  # Debit spread
                    StrategyType.DIAGONAL_CALL_SPREAD, # Can be debit or credit, but often debit
                    StrategyType.DIAGONAL_PUT_SPREAD,  # Can be debit or credit, but often debit

                    # Ratio buying strategies (backspreads are net debit)
                    StrategyType.RATIO_CALL_BACKSPREAD,
                    StrategyType.RATIO_PUT_BACKSPREAD,

                    # Collar strategies (often net zero or small debit/credit)
                    StrategyType.COLLAR,

                    # Synthetic buying strategies
                    StrategyType.SYNTHETIC_LONG,
                    StrategyType.SYNTHETIC_CALL, # Synthetic long call
                    StrategyType.SYNTHETIC_PUT,  # Synthetic long put

                    # Complex Multi-leg buying strategies
                    StrategyType.BUTTERFLY_SPREAD, # Can be debit or credit, but often debit
                    StrategyType.CONDOR_SPREAD, # Can be debit or credit, but often debit
                    StrategyType.CHRISTMAS_TREE, # Often a debit strategy

                    # Indian market specific buying strategies
                    StrategyType.WEEKLY_EXPIRY_STRADDLE,
                    StrategyType.MONTHLY_EXPIRY_STRANGLE,
                    StrategyType.BANKNIFTY_BUTTERFLY # Assuming this is a buying butterfly
                ],
                'min_probability_of_profit': 0.35,  # Lowered for more strategies
                'max_risk_per_trade': 0.02,  # 2% of capital
                'min_risk_reward_ratio': 1.2,  # Lowered for more strategies
                'max_strategies_per_underlying': 100,  # Increased for more strategies
                'optimization_iterations': 100,
                'atm_threshold': 0.02,  # 2% from spot for ATM
                'otm_threshold': 0.05,  # 5% from spot for OTM
                'far_otm_threshold': 0.10  # 10% from spot for Far OTM
            }
            logger.info("[CONFIG] Configuration loaded successfully")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load configuration: {e}")
            raise
    
    async def _load_market_data(self):
        """Load current market data for strategy generation"""
        try:
            logger.info("[LOAD] Loading market data...")
            
            for underlying in self.config['underlying_symbols']:
                # Load latest option chain data
                chain_files = list(self.option_chains_path.glob(f"{underlying}_*_chain_*.parquet"))
                
                if chain_files:
                    latest_file = max(chain_files, key=lambda x: x.stat().st_mtime)
                    chain_data = pl.read_parquet(latest_file)
                    self.market_data_cache[underlying] = chain_data
                    logger.info(f"[LOAD] Loaded option chain for {underlying}: {chain_data.height} contracts")
                else:
                    logger.warning(f"[WARNING] No option chain data found for {underlying}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load market data: {e}")
    
    async def start(self, **kwargs) -> bool:
        """Enhanced start method with comprehensive strategy generation"""
        try:
            # Get trading mode from kwargs
            trading_mode = kwargs.get('trading_mode', 'demo')
            logger.info(f"[START] Starting Enhanced Options Strategy Generation Agent in {trading_mode.upper()} mode...")

            self.is_running = True

            # Extract date parameters if provided (for training pipeline)
            from_date = kwargs.get('from_date')
            to_date = kwargs.get('to_date')

            if from_date and to_date:
                logger.info("[MODE] Training pipeline mode - generating comprehensive strategies")
                # Generate comprehensive strategies for training
                success = await self._generate_comprehensive_strategies(from_date, to_date)
                return success
            else:
                logger.info("[MODE] Live mode - generating real-time strategies")
                # Generate strategies for each underlying
                for underlying in self.config['underlying_symbols']:
                    await self._generate_strategies_for_underlying(underlying)

                # Save generated strategies
                await self._save_strategies()

                logger.info("[SUCCESS] Strategy generation completed")
                return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False
    
    async def _generate_comprehensive_strategies(self, from_date: str, to_date: str) -> bool:
        """Generate comprehensive strategies for training pipeline"""
        try:
            logger.info("[COMPREHENSIVE] Starting comprehensive strategy generation...")
            
            # Load feature data for strategy generation
            for underlying in self.config['underlying_symbols']:
                logger.info(f"[COMPREHENSIVE] Processing {underlying} strategies...")
                
                # Load feature data for all timeframes
                for timeframe in ["1min", "3min", "5min", "15min"]:
                    logger.info(f"[COMPREHENSIVE] Generating {timeframe} strategies for {underlying}...")
                    
                    # Load feature data
                    feature_data = await self._load_feature_data(underlying, timeframe)
                    
                    if feature_data is None or feature_data.height == 0:
                        logger.warning(f"[WARNING] No feature data found for {underlying} {timeframe}")
                        continue
                    
                    # Generate comprehensive strategies including deep OTM and intraday
                    strategies = await self._generate_comprehensive_strategy_set(
                        underlying, timeframe, feature_data
                    )
                    
                    if strategies:
                        # Save strategies with proper naming
                        await self._save_comprehensive_strategies(strategies, underlying, timeframe)
                        logger.info(f"[SUCCESS] Generated {len(strategies)} strategies for {underlying} {timeframe}")
            
            logger.info("[SUCCESS] Comprehensive strategy generation completed")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Comprehensive strategy generation failed: {e}")
            return False
    
    async def _load_feature_data(self, underlying: str, timeframe: str) -> Optional[pl.DataFrame]:
        """Load feature data for strategy generation"""
        try:
            feature_path = self.features_path / timeframe
            
            # Look for feature files
            patterns = [
                f"feature_{underlying}_{timeframe}_*.parquet",
                f"feature_{underlying}_*.parquet",
                f"*{underlying}*feature*.parquet"
            ]
            
            files = []
            for pattern in patterns:
                files.extend(list(feature_path.glob(pattern)))
            
            if not files:
                logger.warning(f"[LOAD] No feature data found for {underlying} {timeframe}")
                return None
            
            # Load the most recent file
            latest_file = max(files, key=lambda x: x.stat().st_mtime)
            df = pl.read_parquet(latest_file)
            
            logger.info(f"[LOAD] Loaded {df.height} feature records for {underlying} {timeframe}")
            return df
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to load feature data: {e}")
            return None
    
    async def _generate_comprehensive_strategy_set(self, underlying: str, timeframe: str, 
                                                 feature_data: pl.DataFrame) -> List[OptionsStrategy]:
        """Generate comprehensive strategy set including deep OTM and intraday strategies"""
        try:
            logger.info(f"[STRATEGY] Generating comprehensive strategies for {underlying} {timeframe}...")
            
            strategies = []
            
            # Get current market conditions from features
            market_conditions = await self._analyze_market_conditions(feature_data)
            
            # 1. Basic Directional Strategies
            strategies.extend(await self._generate_directional_strategies(underlying, feature_data, market_conditions))
            
            # 2. ATM Strategies
            strategies.extend(await self._generate_atm_strategies(underlying, feature_data, market_conditions))
            
            # 3. OTM Strategies
            strategies.extend(await self._generate_otm_strategies(underlying, feature_data, market_conditions))
            
            # 4. Deep OTM Strategies (Key for understanding extreme market moves)
            strategies.extend(await self._generate_deep_otm_strategies(underlying, feature_data, market_conditions))
            
            # 5. Intraday Strategies (Time-sensitive strategies)
            if timeframe in ["1min", "3min", "5min"]:
                strategies.extend(await self._generate_intraday_strategies(underlying, feature_data, market_conditions, timeframe))
            
            # 6. Volatility-based Strategies
            strategies.extend(await self._generate_volatility_strategies(underlying, feature_data, market_conditions))
            
            # 7. Gamma Scalping Strategies
            strategies.extend(await self._generate_gamma_scalping_strategies(underlying, feature_data, market_conditions))
            
            # 8. Market Regime Specific Strategies
            strategies.extend(await self._generate_regime_specific_strategies(underlying, feature_data, market_conditions))
            
            logger.info(f"[SUCCESS] Generated {len(strategies)} comprehensive strategies for {underlying} {timeframe}")
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate comprehensive strategy set: {e}")
            return []
    
    async def _analyze_market_conditions(self, feature_data: pl.DataFrame) -> Dict[str, Any]:
        """Analyze current market conditions from feature data"""
        try:
            # Get latest row for current conditions
            if feature_data.height == 0:
                return {}
            
            latest = feature_data.tail(1)
            
            conditions = {
                'volatility_regime': 'medium',  # Default
                'market_phase': 'sideways',     # Default
                'trend_strength': 0.5,         # Default
                'vix_level': 20.0,             # Default
                'time_to_expiry': 7,           # Default
                'intraday_period': 'normal'    # Default
            }
            
            # Extract conditions from features if available
            if 'rolling_volatility_20' in latest.columns:
                vol = latest.select('rolling_volatility_20').item(0, 0)
                if vol > 30:
                    conditions['volatility_regime'] = 'high'
                elif vol < 15:
                    conditions['volatility_regime'] = 'low'
            
            if 'market_phase' in latest.columns:
                conditions['market_phase'] = latest.select('market_phase').item(0, 0)
            
            if 'high_vol_period' in latest.columns:
                if latest.select('high_vol_period').item(0, 0) == 1:
                    conditions['intraday_period'] = 'high_vol'
            
            if 'closing_period' in latest.columns:
                if latest.select('closing_period').item(0, 0) == 1:
                    conditions['intraday_period'] = 'closing'
            
            return conditions
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze market conditions: {e}")
            return {}
    
    async def _generate_deep_otm_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                          market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate deep OTM strategies for extreme market moves"""
        try:
            strategies = []

            # Simple strategy generation - just create one basic strategy
            spot_price = 25000 if underlying == 'NIFTY' else 52000  # Default values
            deep_call_strike = spot_price + (500 if underlying == 'NIFTY' else 1000)

            # Create a simple deep OTM call strategy
            strategy = OptionsStrategy(
                strategy_id=f"deep_otm_call_{underlying}_{int(deep_call_strike)}",
                strategy_type=StrategyType.LONG_CALL,
                underlying=underlying,
                legs=[OptionsLeg(
                    symbol=f"{underlying}_{int(deep_call_strike)}_CE",
                    option_type='CE',
                    strike_price=deep_call_strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=5.0,
                    underlying=underlying
                )],
                max_profit=float('inf'),
                max_loss=5.0,
                break_even_points=[deep_call_strike + 5.0],
                probability_of_profit=0.15,
                net_premium=-5.0,
                margin_required=5.0,
                risk_reward_ratio=20.0,
                target_profit=100.0,
                stop_loss=2.5,
                created_at=datetime.now(),
                entry_conditions=[{"indicator": "Price", "operator": "break_above", "value": deep_call_strike + 10}],
                exit_conditions=[{"indicator": "Price", "operator": "fall_below", "value": deep_call_strike + 2}]
            )
            strategies.append(strategy)

            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate deep OTM strategies: {e}")
            return []
    
    async def _generate_intraday_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                          market_conditions: Dict, timeframe: str) -> List[OptionsStrategy]:
        """Generate intraday-specific strategies"""
        try:
            strategies = []
            # Simple intraday strategy generation
            logger.info(f"[INTRADAY] Generated {len(strategies)} intraday strategies for {underlying} {timeframe}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate intraday strategies: {e}")
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate intraday strategies: {e}")
            return []
    
    async def _generate_gamma_scalping_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                                market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate gamma scalping strategies"""
        try:
            strategies = []
            # Simple gamma scalping strategy generation
            logger.info(f"[GAMMA] Generated {len(strategies)} gamma scalping strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate gamma scalping strategies: {e}")
            return []

    async def _generate_directional_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                             market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate basic directional strategies"""
        try:
            strategies = []

            # Create a simple long call strategy
            spot_price = 25000 if underlying == 'NIFTY' else 52000
            atm_strike = round(spot_price / 50) * 50 if underlying == 'NIFTY' else round(spot_price / 100) * 100

            # Simple long call strategy
            strategy = OptionsStrategy(
                strategy_id=f"long_call_{underlying}_{int(atm_strike)}",
                strategy_type=StrategyType.LONG_CALL,
                underlying=underlying,
                legs=[OptionsLeg(
                    symbol=f"{underlying}_{int(atm_strike)}_CE",
                    option_type='CE',
                    strike_price=atm_strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=50.0,
                    underlying=underlying
                )],
                max_profit=float('inf'),
                max_loss=50.0,
                break_even_points=[atm_strike + 50.0],
                probability_of_profit=0.4,
                net_premium=-50.0,
                margin_required=50.0,
                risk_reward_ratio=5.0,
                target_profit=250.0,
                stop_loss=25.0,
                created_at=datetime.now(),
                entry_conditions=[{"indicator": "SMA", "operator": "cross_above", "value": 200}],
                exit_conditions=[{"indicator": "SMA", "operator": "cross_below", "value": 200}]
            )
            strategies.append(strategy)

            logger.info(f"[DIRECTIONAL] Generated {len(strategies)} directional strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate directional strategies: {e}")
            return []

    async def _generate_atm_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                     market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate ATM-specific strategies"""
        try:
            strategies = []
            # Simple ATM strategy generation
            logger.info(f"[ATM] Generated {len(strategies)} ATM strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM strategies: {e}")
            return []

    async def _generate_otm_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                     market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate OTM-specific strategies"""
        try:
            strategies = []
            # Simple OTM strategy generation
            logger.info(f"[OTM] Generated {len(strategies)} OTM strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate OTM strategies: {e}")
            return []

    async def _generate_far_otm_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                         market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate Far OTM strategies for high probability trades"""
        try:
            strategies = []
            # Simple Far OTM strategy generation
            logger.info(f"[FAR_OTM] Generated {len(strategies)} Far OTM strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate Far OTM strategies: {e}")
            return []

    async def _generate_volatility_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                            market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate volatility-based strategies"""
        try:
            strategies = []
            # Simple volatility strategy generation
            logger.info(f"[VOLATILITY] Generated {len(strategies)} volatility strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate volatility strategies: {e}")
            return []

    async def _generate_spread_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                        market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate spread strategies"""
        try:
            strategies = []
            # Simple spread strategy generation
            logger.info(f"[SPREAD] Generated {len(strategies)} spread strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate spread strategies: {e}")
            return []

    async def _generate_weekly_expiry_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                               market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate weekly expiry specific strategies"""
        try:
            strategies = []
            # Simple weekly expiry strategy generation
            logger.info(f"[WEEKLY] Generated {len(strategies)} weekly expiry strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate weekly expiry strategies: {e}")
            return []

    async def _analyze_market_conditions(self, feature_data: pl.DataFrame) -> Dict:
        """Analyze current market conditions from feature data"""
        try:
            # Get latest data point
            if feature_data.height == 0:
                return {}

            # Extract key market indicators (using safe column access)
            conditions = {
                'volatility': 0.2,  # Default values
                'trend': 0.0,
                'momentum': 0.0,
                'support_resistance': 0.0
            }

            # Try to get actual values if columns exist
            latest = feature_data.tail(1)
            if 'volatility' in feature_data.columns:
                conditions['volatility'] = latest.select('volatility').item()
            if 'trend_strength' in feature_data.columns:
                conditions['trend'] = latest.select('trend_strength').item()
            if 'momentum' in feature_data.columns:
                conditions['momentum'] = latest.select('momentum').item()

            return conditions

        except Exception as e:
            logger.error(f"[ERROR] Failed to analyze market conditions: {e}")
            return {}

    async def _save_comprehensive_strategies(self, strategies: List[OptionsStrategy], underlying: str, timeframe: str):
        """Save comprehensive strategies to file"""
        try:
            if not strategies:
                logger.warning(f"[SAVE] No strategies to save for {underlying} {timeframe}")
                return

            # Create strategy data for saving with proper type conversion
            strategy_data = []
            for strategy in strategies:
                # Convert break_even_points to string
                break_even_str = ','.join([str(float(bp)) for bp in strategy.break_even_points]) if strategy.break_even_points else ''

                strategy_dict = {
                    'strategy_id': str(strategy.strategy_id),
                    'strategy_type': str(strategy.strategy_type.value),
                    'underlying': str(strategy.underlying),
                    'max_profit': float(strategy.max_profit) if strategy.max_profit != float('inf') else 999999.0,
                    'max_loss': float(strategy.max_loss),
                    'break_even_points': break_even_str,
                    'probability_of_profit': float(strategy.probability_of_profit),
                    'risk_reward_ratio': float(strategy.risk_reward_ratio),
                    'net_premium': float(strategy.net_premium),
                    'margin_required': float(strategy.margin_required),
                    'target_profit': float(strategy.target_profit),
                    'stop_loss': float(strategy.stop_loss),
                    'created_at': str(strategy.created_at.isoformat()),
                    'timeframe': str(timeframe),
                    'num_legs': len(strategy.legs)
                }
                strategy_data.append(strategy_dict)

            # Save to JSON file to avoid parquet Object datatype issues
            if strategy_data:
                import json
                filename = f"strategies_{underlying}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                filepath = self.strategies_path / filename

                with open(filepath, 'w') as f:
                    json.dump(strategy_data, f, indent=2)

                logger.info(f"[SAVE] Saved {len(strategies)} strategies for {underlying} {timeframe} to {filename}")
            else:
                logger.warning(f"[SAVE] No valid strategy data to save for {underlying} {timeframe}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save strategies: {e}")

    async def _generate_regime_specific_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                                 market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate market regime specific strategies"""
        try:
            strategies = []
            # Simple regime-based strategy generation
            logger.info(f"[REGIME] Generated {len(strategies)} regime-specific strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate regime-specific strategies: {e}")
            return []
    
    async def _save_comprehensive_strategies_old(self, strategies: List[OptionsStrategy],
                                           underlying: str, timeframe: str):
        """Save comprehensive strategies with proper naming - OLD METHOD (DISABLED)"""
        # This method is disabled - using the new JSON-based method instead
        logger.info(f"[SAVE] Using new JSON-based save method for {underlying} {timeframe}")
        return
    
    async def _generate_strategies_for_underlying(self, underlying: str):
        """Generate strategies for specific underlying"""
        try:
            logger.info(f"[GENERATE] Generating strategies for {underlying}...")
            
            if underlying not in self.market_data_cache:
                logger.warning(f"[WARNING] No market data for {underlying}")
                return
            
            option_chain = self.market_data_cache[underlying]
            
            # Get current spot price (estimated from ATM options)
            spot_price = self._estimate_spot_price(option_chain)
            
            strategies = []
            
            # Generate each strategy type
            for strategy_type in self.config['strategy_types']:
                strategy_list = await self._generate_strategy_type(
                    strategy_type, underlying, option_chain, spot_price
                )
                strategies.extend(strategy_list)
            
            # Filter and optimize strategies
            filtered_strategies = await self._filter_strategies(strategies)
            optimized_strategies = await self._optimize_strategies(filtered_strategies)
            
            # Store strategies
            self.generated_strategies[underlying] = optimized_strategies
            
            logger.info(f"[SUCCESS] Generated {len(optimized_strategies)} strategies for {underlying}")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate strategies for {underlying}: {e}")
    
    def _estimate_spot_price(self, option_chain: pl.DataFrame) -> float:
        """Estimate current spot price from option chain"""
        try:
            # Find ATM options (closest to spot)
            strikes = option_chain['strike_price'].unique().sort().to_list()

            if not strikes:
                return 25000.0  # Default for Nifty

            # Use middle strike as approximation
            mid_strike = strikes[len(strikes) // 2]
            return mid_strike

        except Exception as e:
            logger.warning(f"[WARNING] Failed to estimate spot price: {e}")
            return 25000.0

    def _get_atm_strikes(self, option_chain: pl.DataFrame, spot_price: float) -> List[float]:
        """Get ATM strike prices within threshold"""
        try:
            strikes = option_chain['strike_price'].unique().sort().to_list()
            atm_threshold = self.config['atm_threshold']

            atm_strikes = []
            for strike in strikes:
                if abs(strike - spot_price) / spot_price <= atm_threshold:
                    atm_strikes.append(strike)

            return atm_strikes if atm_strikes else [min(strikes, key=lambda x: abs(x - spot_price))]

        except Exception as e:
            logger.warning(f"[WARNING] Failed to get ATM strikes: {e}")
            return [spot_price]

    def _get_otm_strikes(self, option_chain: pl.DataFrame, spot_price: float, option_type: str) -> List[float]:
        """Get OTM strike prices"""
        try:
            strikes = option_chain['strike_price'].unique().sort().to_list()
            otm_threshold = self.config['otm_threshold']

            otm_strikes = []
            for strike in strikes:
                if option_type == 'CE' and strike > spot_price:
                    # OTM calls are above spot
                    if (strike - spot_price) / spot_price <= otm_threshold:
                        otm_strikes.append(strike)
                elif option_type == 'PE' and strike < spot_price:
                    # OTM puts are below spot
                    if (spot_price - strike) / spot_price <= otm_threshold:
                        otm_strikes.append(strike)

            return otm_strikes

        except Exception as e:
            logger.warning(f"[WARNING] Failed to get OTM strikes: {e}")
            return []

    def _get_far_otm_strikes(self, option_chain: pl.DataFrame, spot_price: float, option_type: str) -> List[float]:
        """Get Far OTM strike prices"""
        try:
            strikes = option_chain['strike_price'].unique().sort().to_list()
            far_otm_threshold = self.config['far_otm_threshold']

            far_otm_strikes = []
            for strike in strikes:
                if option_type == 'CE' and strike > spot_price:
                    # Far OTM calls are well above spot
                    if (strike - spot_price) / spot_price >= self.config['otm_threshold'] and \
                       (strike - spot_price) / spot_price <= far_otm_threshold:
                        far_otm_strikes.append(strike)
                elif option_type == 'PE' and strike < spot_price:
                    # Far OTM puts are well below spot
                    if (spot_price - strike) / spot_price >= self.config['otm_threshold'] and \
                       (spot_price - strike) / spot_price <= far_otm_threshold:
                        far_otm_strikes.append(strike)

            return far_otm_strikes

        except Exception as e:
            logger.warning(f"[WARNING] Failed to get Far OTM strikes: {e}")
            return []
    
    async def _generate_strategy_type(self, strategy_type: StrategyType, underlying: str,
                                    option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate strategies of specific type"""
        try:
            strategies = []

            # Basic directional strategies
            if strategy_type == StrategyType.LONG_CALL:
                strategies = await self._generate_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.LONG_PUT:
                strategies = await self._generate_long_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SHORT_CALL:
                strategies = await self._generate_short_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SHORT_PUT:
                strategies = await self._generate_short_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.COVERED_CALL:
                strategies = await self._generate_covered_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.PROTECTIVE_PUT:
                strategies = await self._generate_protective_put_strategies(underlying, option_chain, spot_price)

            # ATM strategies
            elif strategy_type == StrategyType.ATM_LONG_CALL:
                strategies = await self._generate_atm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.ATM_LONG_PUT:
                strategies = await self._generate_atm_long_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.ATM_SHORT_CALL:
                strategies = await self._generate_atm_short_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.ATM_SHORT_PUT:
                strategies = await self._generate_atm_short_put_strategies(underlying, option_chain, spot_price)

            # OTM strategies
            elif strategy_type == StrategyType.OTM_LONG_CALL:
                strategies = await self._generate_otm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.OTM_LONG_PUT:
                strategies = await self._generate_otm_long_put_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.OTM_SHORT_CALL:
                strategies = await self._generate_otm_short_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.OTM_SHORT_PUT:
                strategies = await self._generate_otm_short_put_strategies(underlying, option_chain, spot_price)

            # Far OTM strategies
            elif strategy_type == StrategyType.FAR_OTM_LONG_CALL:
                strategies = await self._generate_far_otm_long_call_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.FAR_OTM_LONG_PUT:
                strategies = await self._generate_far_otm_long_put_strategies(underlying, option_chain, spot_price)

            # Volatility strategies
            elif strategy_type == StrategyType.LONG_STRADDLE:
                strategies = await self._generate_long_straddle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SHORT_STRADDLE:
                strategies = await self._generate_short_straddle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.LONG_STRANGLE:
                strategies = await self._generate_long_strangle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SHORT_STRANGLE:
                strategies = await self._generate_short_strangle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.IRON_CONDOR:
                strategies = await self._generate_iron_condor_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.IRON_BUTTERFLY:
                strategies = await self._generate_iron_butterfly_strategies(underlying, option_chain, spot_price)

            # Spread strategies
            elif strategy_type == StrategyType.BULL_CALL_SPREAD:
                strategies = await self._generate_bull_call_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.BEAR_CALL_SPREAD:
                strategies = await self._generate_bear_call_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.BULL_PUT_SPREAD:
                strategies = await self._generate_bull_put_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.BEAR_PUT_SPREAD:
                strategies = await self._generate_bear_put_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.CALL_CALENDAR_SPREAD:
                strategies = await self._generate_call_calendar_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.PUT_CALENDAR_SPREAD:
                strategies = await self._generate_put_calendar_spread_strategies(underlying, option_chain, spot_price)

            # Ratio strategies
            elif strategy_type == StrategyType.RATIO_CALL_SPREAD:
                strategies = await self._generate_ratio_call_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.RATIO_PUT_SPREAD:
                strategies = await self._generate_ratio_put_spread_strategies(underlying, option_chain, spot_price)

            # Complex strategies
            elif strategy_type == StrategyType.COLLAR:
                strategies = await self._generate_collar_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.BUTTERFLY_SPREAD:
                strategies = await self._generate_butterfly_spread_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.JADE_LIZARD:
                strategies = await self._generate_jade_lizard_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.SYNTHETIC_LONG:
                strategies = await self._generate_synthetic_long_strategies(underlying, option_chain, spot_price)

            # Indian market specific
            elif strategy_type == StrategyType.WEEKLY_EXPIRY_STRADDLE:
                strategies = await self._generate_weekly_expiry_straddle_strategies(underlying, option_chain, spot_price)
            elif strategy_type == StrategyType.MONTHLY_EXPIRY_STRANGLE:
                strategies = await self._generate_monthly_expiry_strangle_strategies(underlying, option_chain, spot_price)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate {strategy_type.value} strategies: {e}")
            return []
    
    async def _generate_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                           spot_price: float) -> List[OptionsStrategy]:
        """Generate long call strategies"""
        try:
            strategies = []
            
            # Get call options
            calls = option_chain.filter(pl.col('option_type') == 'CE')
            
            for call_row in calls.iter_rows(named=True):
                # Create single leg strategy
                leg = OptionsLeg(
                    symbol=call_row['symbol'],
                    option_type='CE',
                    strike_price=call_row['strike_price'],
                    expiry_date='2024-01-25',  # Sample expiry
                    quantity=1,
                    premium=call_row['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                max_profit = float('inf')  # Unlimited upside
                max_loss = call_row['ltp']
                break_even = call_row['strike_price'] + call_row['ltp']
                
                # Estimate probability of profit (simplified)
                prob_profit = max(0.0, min(1.0, (spot_price - break_even) / spot_price + 0.5))
                
                strategy = OptionsStrategy(
                    strategy_id=f"LC_{underlying}_{call_row['strike_price']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call_row['ltp'],  # Negative because we pay premium
                    margin_required=call_row['ltp'],
                    risk_reward_ratio=float('inf') if max_loss > 0 else 0,
                    target_profit=max_loss * 2,  # 200% return target
                    stop_loss=max_loss * 0.5,  # 50% stop loss
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Price", "operator": "break_above", "value": spot_price * 1.01}],
                    exit_conditions=[{"indicator": "Price", "operator": "fall_below", "value": spot_price * 0.99}]
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long call strategies: {e}")
            return []
    
    async def _generate_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                          spot_price: float) -> List[OptionsStrategy]:
        """Generate long put strategies"""
        try:
            strategies = []
            
            # Get put options
            puts = option_chain.filter(pl.col('option_type') == 'PE')
            
            for put_row in puts.iter_rows(named=True):
                # Create single leg strategy
                leg = OptionsLeg(
                    symbol=put_row['symbol'],
                    option_type='PE',
                    strike_price=put_row['strike_price'],
                    expiry_date='2024-01-25',  # Sample expiry
                    quantity=1,
                    premium=put_row['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                max_profit = put_row['strike_price'] - put_row['ltp']
                max_loss = put_row['ltp']
                break_even = put_row['strike_price'] - put_row['ltp']
                
                # Estimate probability of profit
                prob_profit = max(0.0, min(1.0, (break_even - spot_price) / spot_price + 0.5))
                
                strategy = OptionsStrategy(
                    strategy_id=f"LP_{underlying}_{put_row['strike_price']}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put_row['ltp'],
                    margin_required=put_row['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "RSI", "operator": "<", "value": 30}], # Placeholder
                    exit_conditions=[{"indicator": "RSI", "operator": ">", "value": 70}] # Placeholder
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long put strategies: {e}")
            return []
    
    async def _generate_long_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate long straddle strategies"""
        try:
            strategies = []
            
            # Get unique strikes
            strikes = option_chain['strike_price'].unique().sort().to_list()
            
            for strike in strikes:
                # Get call and put at same strike
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') & 
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') & 
                    (pl.col('strike_price') == strike)
                ).height > 0 else None
                
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') & 
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') & 
                    (pl.col('strike_price') == strike)
                ).height > 0 else None
                
                if not call or not put:
                    continue
                
                # Create legs
                call_leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=call['ltp'],
                    underlying=underlying
                )
                
                put_leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=put['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                total_premium = call['ltp'] + put['ltp']
                max_profit = float('inf')  # Unlimited if big move
                max_loss = total_premium
                break_even_upper = strike + total_premium
                break_even_lower = strike - total_premium
                
                # Estimate probability of profit (move beyond break-evens)
                prob_profit = 0.4  # Simplified estimate for volatility strategy
                
                strategy = OptionsStrategy(
                    strategy_id=f"LS_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_STRADDLE,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=-total_premium,
                    margin_required=total_premium,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "IV", "operator": "low"}], # Placeholder
                    exit_conditions=[{"indicator": "IV", "operator": "high"}] # Placeholder
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long straddle strategies: {e}")
            return []
    
    async def _generate_long_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate long strangle strategies"""
        try:
            strategies = []

            # Get OTM strikes for calls and puts
            otm_call_strikes = self._get_otm_strikes(option_chain, spot_price, 'CE')
            otm_put_strikes = self._get_otm_strikes(option_chain, spot_price, 'PE')

            for call_strike in otm_call_strikes[:3]:  # Limit to top 3
                for put_strike in otm_put_strikes[:3]:
                    if call_strike <= put_strike:
                        continue

                    # Get call and put options
                    call = option_chain.filter(
                        (pl.col('option_type') == 'CE') &
                        (pl.col('strike_price') == call_strike)
                    ).row(0, named=True) if option_chain.filter(
                        (pl.col('option_type') == 'CE') &
                        (pl.col('strike_price') == call_strike)
                    ).height > 0 else None

                    put = option_chain.filter(
                        (pl.col('option_type') == 'PE') &
                        (pl.col('strike_price') == put_strike)
                    ).row(0, named=True) if option_chain.filter(
                        (pl.col('option_type') == 'PE') &
                        (pl.col('strike_price') == put_strike)
                    ).height > 0 else None

                    if not call or not put:
                        continue

                    # Create legs
                    call_leg = OptionsLeg(
                        symbol=call['symbol'],
                        option_type='CE',
                        strike_price=call_strike,
                        expiry_date='2024-01-25',
                        quantity=1,
                        premium=call['ltp'],
                        underlying=underlying
                    )

                    put_leg = OptionsLeg(
                        symbol=put['symbol'],
                        option_type='PE',
                        strike_price=put_strike,
                        expiry_date='2024-01-25',
                        quantity=1,
                        premium=put['ltp'],
                        underlying=underlying
                    )

                    # Calculate strategy metrics
                    total_premium = call['ltp'] + put['ltp']
                    max_profit = float('inf')  # Unlimited if big move
                    max_loss = total_premium
                    break_even_upper = call_strike + total_premium
                    break_even_lower = put_strike - total_premium

                    # Estimate probability of profit
                    prob_profit = 0.35  # Lower than straddle due to wider strikes

                    strategy = OptionsStrategy(
                        strategy_id=f"LST_{underlying}_{call_strike}_{put_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                        strategy_type=StrategyType.LONG_STRANGLE,
                        underlying=underlying,
                        legs=[call_leg, put_leg],
                        max_profit=max_profit,
                        max_loss=max_loss,
                        break_even_points=[break_even_lower, break_even_upper],
                        probability_of_profit=prob_profit,
                        net_premium=-total_premium,
                        margin_required=total_premium,
                        risk_reward_ratio=float('inf'),
                        target_profit=max_loss * 2,
                        stop_loss=max_loss * 0.5,
                        created_at=datetime.now(),
                        entry_conditions=[{"indicator": "IV", "operator": "low"}], # Placeholder
                        exit_conditions=[{"indicator": "IV", "operator": "high"}] # Placeholder
                    )

                    strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long strangle strategies: {e}")
            return []
    
    # ATM Strategy Methods
    async def _generate_atm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long call strategies"""
        try:
            strategies = []
            atm_strikes = self._get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not call:
                    continue

                leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=call['ltp'],
                    underlying=underlying
                )

                max_profit = float('inf')
                max_loss = call['ltp']
                break_even = strike + call['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call['ltp'],
                    margin_required=call['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": ">", "value": 10000}], # Placeholder
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM long call strategies: {e}")
            return []

    async def _generate_atm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                              spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long put strategies"""
        try:
            strategies = []
            atm_strikes = self._get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not put:
                    continue

                leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=put['ltp'],
                    underlying=underlying
                )

                max_profit = strike - put['ltp']
                max_loss = put['ltp']
                break_even = strike - put['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put['ltp'],
                    margin_required=put['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": ">", "value": 10000}], # Placeholder
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM long put strategies: {e}")
            return []

    async def _generate_atm_short_call_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM short call strategies"""
        try:
            strategies = []
            atm_strikes = self._get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not call:
                    continue

                leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=-1,  # Short position
                    premium=call['ltp'],
                    underlying=underlying
                )

                max_profit = call['ltp']
                max_loss = float('inf')  # Unlimited loss
                break_even = strike + call['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMSC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_SHORT_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=call['ltp'],  # Positive because we receive premium
                    margin_required=call['ltp'] * 10,  # Estimated margin
                    risk_reward_ratio=0,  # Unlimited loss
                    target_profit=max_profit * 0.8,  # 80% of max profit
                    stop_loss=max_profit * 2,  # 200% of premium received
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Delta", "operator": "near_zero"}], # Placeholder
                    exit_conditions=[{"indicator": "Delta", "operator": "far_from_zero"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM short call strategies: {e}")
            return []

    async def _generate_atm_short_put_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM short put strategies"""
        try:
            strategies = []
            atm_strikes = self._get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not put:
                    continue

                leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=-1,  # Short position
                    premium=put['ltp'],
                    underlying=underlying
                )

                max_profit = put['ltp']
                max_loss = strike - put['ltp']
                break_even = strike - put['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMSP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_SHORT_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=put['ltp'],  # Positive because we receive premium
                    margin_required=put['ltp'] * 10,  # Estimated margin
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,  # 80% of max profit
                    stop_loss=max_profit * 2,  # 200% of premium received
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Delta", "operator": "near_zero"}], # Placeholder
                    exit_conditions=[{"indicator": "Delta", "operator": "far_from_zero"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM short put strategies: {e}")
            return []

    async def _generate_bull_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                  spot_price: float) -> List[OptionsStrategy]:
        """Generate bull call spread strategies"""
        # Buy lower strike call, sell higher strike call
        # Implementation would create two-leg spread strategies
        return []
    
    async def _generate_bear_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                 spot_price: float) -> List[OptionsStrategy]:
        """Generate bear put spread strategies"""
        # Buy higher strike put, sell lower strike put
        # Implementation would create two-leg spread strategies
        return []

    async def _generate_iron_condor_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                             spot_price: float) -> List[OptionsStrategy]:
        """Generate iron condor strategies"""
        # Four-leg strategy: sell call spread + sell put spread
        # Implementation would create four-leg strategies
        return []

    # Placeholder methods for remaining strategies
    async def _generate_short_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate short call strategies"""
        return []

    async def _generate_short_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate short put strategies"""
        return []

    async def _generate_covered_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate covered call strategies"""
        return []

    async def _generate_protective_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate protective put strategies"""
        return []

    async def _generate_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM long call strategies"""
        return []

    async def _generate_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM long put strategies"""
        return []

    async def _generate_otm_short_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM short call strategies"""
        return []

    async def _generate_otm_short_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM short put strategies"""
        return []

    async def _generate_far_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Far OTM long call strategies"""
        return []

    async def _generate_far_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Far OTM long put strategies"""
        return []

    async def _generate_short_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate short straddle strategies"""
        return []

    async def _generate_short_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate short strangle strategies"""
        return []

    async def _generate_iron_butterfly_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate iron butterfly strategies"""
        return []

    async def _generate_bear_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate bear call spread strategies"""
        return []

    async def _generate_bull_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate bull put spread strategies"""
        return []

    async def _generate_call_calendar_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate call calendar spread strategies"""
        return []

    async def _generate_put_calendar_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate put calendar spread strategies"""
        return []

    async def _generate_ratio_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ratio call spread strategies"""
        return []

    async def _generate_ratio_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ratio put spread strategies"""
        return []

    async def _generate_collar_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate collar strategies"""
        return []

    async def _generate_butterfly_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate butterfly spread strategies"""
        return []

    async def _generate_jade_lizard_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate jade lizard strategies"""
        return []

    async def _generate_synthetic_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long strategies"""
        return []

    async def _generate_weekly_expiry_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate weekly expiry straddle strategies"""
        return []

    async def _generate_monthly_expiry_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate monthly expiry strangle strategies"""
        return []
    
    async def _filter_strategies(self, strategies: List[OptionsStrategy]) -> List[OptionsStrategy]:
        """Filter strategies based on criteria"""
        try:
            filtered = []
            
            for strategy in strategies:
                # Apply filters
                if (strategy.probability_of_profit >= self.config['min_probability_of_profit'] and
                    strategy.risk_reward_ratio >= self.config['min_risk_reward_ratio'] and
                    strategy.max_loss <= 10000):  # Max loss limit
                    
                    filtered.append(strategy)
            
            logger.info(f"[FILTER] Filtered {len(filtered)} strategies from {len(strategies)}")
            return filtered
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to filter strategies: {e}")
            return strategies
    
    async def _optimize_strategies(self, strategies: List[OptionsStrategy]) -> List[OptionsStrategy]:
        """Optimize strategy parameters"""
        try:
            # Sort by risk-reward ratio and probability of profit
            optimized = sorted(
                strategies,
                key=lambda s: (s.risk_reward_ratio * s.probability_of_profit),
                reverse=True
            )
            
            # Limit number of strategies per underlying
            max_strategies = self.config['max_strategies_per_underlying']
            optimized = optimized[:max_strategies]
            
            logger.info(f"[OPTIMIZE] Optimized to {len(optimized)} strategies")
            return optimized
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to optimize strategies: {e}")
            return strategies
    
    async def _save_strategies(self):
        """Save generated strategies"""
        try:
            logger.info("[SAVE] Saving generated strategies...")
            
            all_strategies = []
            
            for underlying, strategies in self.generated_strategies.items():
                for strategy in strategies:
                    all_strategies.append(strategy.to_dict())
            
            if all_strategies:
                # Convert to DataFrame and save
                strategies_df = pl.DataFrame(all_strategies)
                
                filename = f"generated_strategies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.parquet"
                filepath = self.strategies_path / filename
                
                strategies_df.write_parquet(filepath)
                
                # Also save as JSON for readability
                json_filename = f"generated_strategies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                json_filepath = self.strategies_path / json_filename
                
                async with aiofiles.open(json_filepath, 'w') as f:
                    await f.write(json.dumps(all_strategies, indent=2, default=str))
                
                logger.info(f"[SUCCESS] Saved {len(all_strategies)} strategies")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save strategies: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Strategy Generation Agent...")
            self.is_running = False
            logger.info("[SUCCESS] Options Strategy Generation Agent cleaned up")
            
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    """Example usage of Options Strategy Generation Agent"""
    agent = OptionsStrategyGenerationAgent()
    
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
#!/usr/bin/env python3
"""
Options Strategy Generation Agent - Dynamic Strategy Creation & Optimization

Features:
📊 1. Strategy Generation
- Directional strategies (Long Call/Put, Covered Call)
- Volatility strategies (Straddle, Strangle, Iron Condor)
- Spread strategies (Bull/Bear spreads, Calendar spreads)
- Complex multi-leg strategies

📈 2. Dynamic Optimization
- Real-time strategy parameter tuning
- Market regime-based strategy selection
- Risk-adjusted strategy optimization
- Greeks-based strategy construction

⚡ 3. Strategy Validation
- Risk-reward analysis
- Probability of profit calculations
- Maximum loss/profit scenarios
- Break-even point analysis

🎯 4. Performance Optimization
- Vectorized strategy calculations
- Polars + PyArrow for fast processing
- Parallel strategy evaluation
- Memory-efficient strategy storage
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import aiofiles
from dataclasses import dataclass, asdict
import json
from enum import Enum
import itertools

# Options pricing
try:
    import py_vollib.black_scholes as bs
    import py_vollib.black_scholes.greeks.analytical as greeks
except ImportError:
    logger.warning("py_vollib not installed. Using fallback calculations.")

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Comprehensive options strategy types for Indian market"""

    # Basic Directional Strategies
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"
    SHORT_CALL = "short_call"
    SHORT_PUT = "short_put"
    COVERED_CALL = "covered_call"
    PROTECTIVE_PUT = "protective_put"
    CASH_SECURED_PUT = "cash_secured_put"

    # ATM Strategies
    ATM_LONG_CALL = "atm_long_call"
    ATM_LONG_PUT = "atm_long_put"
    ATM_SHORT_CALL = "atm_short_call"
    ATM_SHORT_PUT = "atm_short_put"

    # OTM Strategies
    OTM_LONG_CALL = "otm_long_call"
    OTM_LONG_PUT = "otm_long_put"
    OTM_SHORT_CALL = "otm_short_call"
    OTM_SHORT_PUT = "otm_short_put"

    # Far OTM Strategies
    FAR_OTM_LONG_CALL = "far_otm_long_call"
    FAR_OTM_LONG_PUT = "far_otm_long_put"
    FAR_OTM_SHORT_CALL = "far_otm_short_call"
    FAR_OTM_SHORT_PUT = "far_otm_short_put"

    # Volatility Strategies
    LONG_STRADDLE = "long_straddle"
    SHORT_STRADDLE = "short_straddle"
    LONG_STRANGLE = "long_strangle"
    SHORT_STRANGLE = "short_strangle"
    IRON_CONDOR = "iron_condor"
    IRON_BUTTERFLY = "iron_butterfly"
    REVERSE_IRON_CONDOR = "reverse_iron_condor"
    REVERSE_IRON_BUTTERFLY = "reverse_iron_butterfly"

    # Spread Strategies
    BULL_CALL_SPREAD = "bull_call_spread"
    BEAR_CALL_SPREAD = "bear_call_spread"
    BULL_PUT_SPREAD = "bull_put_spread"
    BEAR_PUT_SPREAD = "bear_put_spread"
    CALL_CALENDAR_SPREAD = "call_calendar_spread"
    PUT_CALENDAR_SPREAD = "put_calendar_spread"
    DIAGONAL_CALL_SPREAD = "diagonal_call_spread"
    DIAGONAL_PUT_SPREAD = "diagonal_put_spread"

    # Ratio Strategies
    RATIO_CALL_SPREAD = "ratio_call_spread"
    RATIO_PUT_SPREAD = "ratio_put_spread"
    RATIO_CALL_BACKSPREAD = "ratio_call_backspread"
    RATIO_PUT_BACKSPREAD = "ratio_put_backspread"

    # Collar Strategies
    COLLAR = "collar"
    REVERSE_COLLAR = "reverse_collar"

    # Synthetic Strategies
    SYNTHETIC_LONG = "synthetic_long"
    SYNTHETIC_SHORT = "synthetic_short"
    SYNTHETIC_CALL = "synthetic_call"
    SYNTHETIC_PUT = "synthetic_put"

    # Complex Multi-leg Strategies
    JADE_LIZARD = "jade_lizard"
    REVERSE_JADE_LIZARD = "reverse_jade_lizard"
    BUTTERFLY_SPREAD = "butterfly_spread"
    CONDOR_SPREAD = "condor_spread"
    CHRISTMAS_TREE = "christmas_tree"

    # Indian Market Specific
    WEEKLY_EXPIRY_STRADDLE = "weekly_expiry_straddle"
    MONTHLY_EXPIRY_STRANGLE = "monthly_expiry_strangle"
    NIFTY_IRON_CONDOR = "nifty_iron_condor"
    BANKNIFTY_BUTTERFLY = "banknifty_butterfly"

@dataclass
class OptionsLeg:
    """Single options leg in a strategy"""
    symbol: str
    option_type: str  # CE or PE
    strike_price: float
    expiry_date: str
    quantity: int  # Positive for long, negative for short
    premium: float
    underlying: str

@dataclass
class OptionsStrategy:
    """Complete options strategy definition"""
    strategy_id: str
    strategy_type: StrategyType
    underlying: str
    legs: List[OptionsLeg]
    max_profit: float
    max_loss: float
    break_even_points: List[float]
    probability_of_profit: float
    net_premium: float
    margin_required: float
    risk_reward_ratio: float
    target_profit: float
    stop_loss: float
    created_at: datetime
    entry_conditions: List[Dict]
    exit_conditions: List[Dict]
    
    def to_dict(self) -> Dict:
        """Convert strategy to dictionary"""
        return {
            'strategy_id': self.strategy_id,
            'strategy_type': self.strategy_type.value,
            'underlying': self.underlying,
            'legs': [asdict(leg) for leg in self.legs],
            'max_profit': self.max_profit,
            'max_loss': self.max_loss,
            'break_even_points': self.break_even_points,
            'probability_of_profit': self.probability_of_profit,
            'net_premium': self.net_premium,
            'margin_required': self.margin_required,
            'risk_reward_ratio': self.risk_reward_ratio,
            'target_profit': self.target_profit,
            'stop_loss': self.stop_loss,
            'created_at': self.created_at.isoformat(),
            'entry_conditions': self.entry_conditions,
            'exit_conditions': self.exit_conditions
        }

#!/usr/bin/env python3
"""
Options Strategy Generation Agent - Dynamic Strategy Creation & Optimization

Features:
📊 1. Strategy Generation
- Directional strategies (Long Call/Put, Covered Call)
- Volatility strategies (Straddle, Strangle, Iron Condor)
- Spread strategies (Bull/Bear spreads, Calendar spreads)
- Complex multi-leg strategies

📈 2. Dynamic Optimization
- Real-time strategy parameter tuning
- Market regime-based strategy selection
- Risk-adjusted strategy optimization
- Greeks-based strategy construction

⚡ 3. Strategy Validation
- Risk-reward analysis
- Probability of profit calculations
- Maximum loss/profit scenarios
- Break-even point analysis

🎯 4. Performance Optimization
- Vectorized strategy calculations
- Polars + PyArrow for fast processing
- Parallel strategy evaluation
- Memory-efficient strategy storage
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import aiofiles
from dataclasses import dataclass, asdict
import json
from enum import Enum
import itertools

# Options pricing
try:
    import py_vollib.black_scholes as bs
    import py_vollib.black_scholes.greeks.analytical as greeks
except ImportError:
    logger.warning("py_vollib not installed. Using fallback calculations.")

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Comprehensive options strategy types for Indian market"""

    # Basic Directional Strategies
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"
    SHORT_CALL = "short_call"
    SHORT_PUT = "short_put"
    COVERED_CALL = "covered_call"
    PROTECTIVE_PUT = "protective_put"
    CASH_SECURED_PUT = "cash_secured_put"

    # ATM Strategies
    ATM_LONG_CALL = "atm_long_call"
    ATM_LONG_PUT = "atm_long_put"
    ATM_SHORT_CALL = "atm_short_call"
    ATM_SHORT_PUT = "atm_short_put"

    # OTM Strategies
    OTM_LONG_CALL = "otm_long_call"
    OTM_LONG_PUT = "otm_long_put"
    OTM_SHORT_CALL = "otm_short_call"
    OTM_SHORT_PUT = "otm_short_put"

    # Far OTM Strategies (Deep OTM)
    FAR_OTM_LONG_CALL = "far_otm_long_call"
    FAR_OTM_LONG_PUT = "far_otm_long_put"
    FAR_OTM_SHORT_CALL = "far_otm_short_call"
    FAR_OTM_SHORT_PUT = "far_otm_short_put"
    
    # Deep OTM Strategies (Very Far OTM)
    DEEP_OTM_LONG_CALL = "deep_otm_long_call"
    DEEP_OTM_LONG_PUT = "deep_otm_long_put"
    DEEP_OTM_SHORT_CALL = "deep_otm_short_call"
    DEEP_OTM_SHORT_PUT = "deep_otm_short_put"
    
    # Intraday Strategies
    INTRADAY_SCALPING_CALL = "intraday_scalping_call"
    INTRADAY_SCALPING_PUT = "intraday_scalping_put"
    INTRADAY_MOMENTUM_CALL = "intraday_momentum_call"
    INTRADAY_MOMENTUM_PUT = "intraday_momentum_put"
    INTRADAY_REVERSAL_CALL = "intraday_reversal_call"
    INTRADAY_REVERSAL_PUT = "intraday_reversal_put"
    
    # Gamma Scalping Strategies
    GAMMA_SCALPING_LONG = "gamma_scalping_long"
    GAMMA_SCALPING_SHORT = "gamma_scalping_short"
    DELTA_NEUTRAL_GAMMA_SCALP = "delta_neutral_gamma_scalp"
    
    # Volatility Breakout Strategies
    VOLATILITY_BREAKOUT_LONG = "volatility_breakout_long"
    VOLATILITY_BREAKOUT_SHORT = "volatility_breakout_short"
    VIX_BASED_STRATEGY = "vix_based_strategy"

    # Volatility Strategies
    LONG_STRADDLE = "long_straddle"
    SHORT_STRADDLE = "short_straddle"
    LONG_STRANGLE = "long_strangle"
    SHORT_STRANGLE = "short_strangle"
    IRON_CONDOR = "iron_condor"
    IRON_BUTTERFLY = "iron_butterfly"
    REVERSE_IRON_CONDOR = "reverse_iron_condor"
    REVERSE_IRON_BUTTERFLY = "reverse_iron_butterfly"

    # Spread Strategies
    BULL_CALL_SPREAD = "bull_call_spread"
    BEAR_CALL_SPREAD = "bear_call_spread"
    BULL_PUT_SPREAD = "bull_put_spread"
    BEAR_PUT_SPREAD = "bear_put_spread"
    CALL_CALENDAR_SPREAD = "call_calendar_spread"
    PUT_CALENDAR_SPREAD = "put_calendar_spread"
    DIAGONAL_CALL_SPREAD = "diagonal_call_spread"
    DIAGONAL_PUT_SPREAD = "diagonal_put_spread"

    # Ratio Strategies
    RATIO_CALL_SPREAD = "ratio_call_spread"
    RATIO_PUT_SPREAD = "ratio_put_spread"
    RATIO_CALL_BACKSPREAD = "ratio_call_backspread"
    RATIO_PUT_BACKSPREAD = "ratio_put_backspread"

    # Collar Strategies
    COLLAR = "collar"
    REVERSE_COLLAR = "reverse_collar"

    # Synthetic Strategies
    SYNTHETIC_LONG = "synthetic_long"
    SYNTHETIC_SHORT = "synthetic_short"
    SYNTHETIC_CALL = "synthetic_call"
    SYNTHETIC_PUT = "synthetic_put"

    # Complex Multi-leg Strategies
    JADE_LIZARD = "jade_lizard"
    REVERSE_JADE_LIZARD = "reverse_jade_lizard"
    BUTTERFLY_SPREAD = "butterfly_spread"
    CONDOR_SPREAD = "condor_spread"
    CHRISTMAS_TREE = "christmas_tree"

    # Indian Market Specific
    WEEKLY_EXPIRY_STRADDLE = "weekly_expiry_straddle"
    MONTHLY_EXPIRY_STRANGLE = "monthly_expiry_strangle"
    NIFTY_IRON_CONDOR = "nifty_iron_condor"
    BANKNIFTY_BUTTERFLY = "banknifty_butterfly"

@dataclass
class OptionsLeg:
    """Single options leg in a strategy"""
    symbol: str
    option_type: str  # CE or PE
    strike_price: float
    expiry_date: str
    quantity: int  # Positive for long, negative for short
    premium: float
    underlying: str

@dataclass
class OptionsStrategy:
    """Complete options strategy definition"""
    strategy_id: str
    strategy_type: StrategyType
    underlying: str
    legs: List[OptionsLeg]
    max_profit: float
    max_loss: float
    break_even_points: List[float]
    probability_of_profit: float
    net_premium: float
    margin_required: float
    risk_reward_ratio: float
    target_profit: float
    stop_loss: float
    created_at: datetime
    
    def to_dict(self) -> Dict:
        """Convert strategy to dictionary"""
        return {
            'strategy_id': self.strategy_id,
            'strategy_type': self.strategy_type.value,
            'underlying': self.underlying,
            'legs': [asdict(leg) for leg in self.legs],
            'max_profit': self.max_profit,
            'max_loss': self.max_loss,
            'break_even_points': self.break_even_points,
            'probability_of_profit': self.probability_of_profit,
            'net_premium': self.net_premium,
            'margin_required': self.margin_required,
            'risk_reward_ratio': self.risk_reward_ratio,
            'target_profit': self.target_profit,
            'stop_loss': self.stop_loss,
            'created_at': self.created_at.isoformat()
        }

    async def cleanup(self):
        """Cleanup the agent"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Strategy Generation Agent...")
            self.is_running = False
            self.generated_strategies.clear()
            self.market_data_cache.clear()
            logger.info("[SUCCESS] Options Strategy Generation Agent cleaned up")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup agent: {e}")


if __name__ == "__main__":
    # Demo usage
    import asyncio

    async def main():
        agent = OptionsStrategyGenerationAgent()
        await agent.initialize()
        await agent.start()

    asyncio.run(main())
