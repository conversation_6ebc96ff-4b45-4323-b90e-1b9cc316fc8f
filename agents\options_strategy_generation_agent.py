#!/usr/bin/env python3
"""
Options Strategy Generation Agent - Dynamic Strategy Creation & Optimization

Features:
📊 1. Strategy Generation
- Directional strategies (Long Call/Put, Covered Call)
- Volatility strategies (<PERSON>raddle, Strangle, Iron Condor)
- Spread strategies (Bull/Bear spreads, Calendar spreads)
- Complex multi-leg strategies

📈 2. Dynamic Optimization
- Real-time strategy parameter tuning
- Market regime-based strategy selection
- Risk-adjusted strategy optimization
- Greeks-based strategy construction

⚡ 3. Strategy Validation
- Risk-reward analysis
- Probability of profit calculations
- Maximum loss/profit scenarios
- Break-even point analysis

🎯 4. Performance Optimization
- Vectorized strategy calculations
- Polars + PyArrow for fast processing
- Parallel strategy evaluation
- Memory-efficient strategy storage
"""

import asyncio
import logging
import polars as pl
from datetime import datetime
from pathlib import Path
from typing import Dict, List

# Import refactored modules
from agents.strategy_generation.config_manager import ConfigManager
from agents.strategy_generation.data_models import OptionsStrategy, StrategyType
from agents.strategy_generation.market_analyzer import MarketAnalyzer
from agents.strategy_generation.market_data_loader import MarketDataLoader
from agents.strategy_generation.strategy_calculators import StrategyCalculators
from agents.strategy_generation.strategy_generators import StrategyGenerators
from agents.strategy_generation.strategy_optimizer import StrategyOptimizer
from agents.strategy_generation.strategy_saver import StrategySaver

logger = logging.getLogger(__name__)


class OptionsStrategyGenerationAgent:
    """
    Options Strategy Generation Agent for creating and optimizing options strategies

    IMPORTANT CLARIFICATION:
    This agent generates OPTIONS STRATEGIES (actual option combinations like straddles, spreads, etc.)
    This is different from SIGNAL GENERATION STRATEGIES in config/options_strategies.yaml which define
    WHEN to trade (entry conditions, RSI levels, etc.).

    This agent generates WHAT to trade:
    - Dynamic strategy generation based on market conditions
    - Strategy parameter optimization
    - Risk-reward analysis
    - Strategy validation and filtering
    - Multi-leg strategy construction

    The signal generation strategies from YAML are used by the signal generation agent
    to determine WHEN to execute these options strategies.
    """

    def __init__(self, config_path: str = "config/options_strategy_generation_config.yaml"):
        """Initialize Options Strategy Generation Agent"""
        self.config_path = config_path
        self.config = None
        self.is_running = False

        # Data paths
        self.data_path = Path("data")
        self.strategies_path = self.data_path / "strategies"

        # Create directories
        self.strategies_path.mkdir(parents=True, exist_ok=True)

        # Initialize refactored components
        self.config_manager = ConfigManager(config_path)
        self.market_analyzer = MarketAnalyzer()
        self.market_data_loader = MarketDataLoader(self.data_path)
        self.strategy_calculators = StrategyCalculators()
        self.strategy_saver = StrategySaver(self.strategies_path)

        # These will be initialized after config is loaded
        self.strategy_generators = None
        self.strategy_optimizer = None

        # Strategy cache
        self.generated_strategies = {}

        logger.info("[INIT] Options Strategy Generation Agent initialized")

    async def initialize(self, **kwargs):
        """Initialize the agent with optional parameters"""
        try:
            # Load configuration using config manager
            self.config = await self.config_manager.load_config()

            # Initialize components that depend on config
            self.strategy_generators = StrategyGenerators(self.config, self.strategy_calculators)
            self.strategy_optimizer = StrategyOptimizer(self.config)

            # Store kwargs for later use
            self.init_kwargs = kwargs

            # Load market data using market data loader
            self.market_data_cache = await self.market_data_loader.load_market_data(
                self.config['underlying_symbols']
            )

            logger.info("[SUCCESS] Options Strategy Generation Agent initialized successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to initialize agent: {e}")
            return False

    async def start(self, **kwargs) -> bool:
        """Enhanced start method with comprehensive strategy generation"""
        try:
            # Get trading mode from kwargs
            trading_mode = kwargs.get('trading_mode', 'demo')
            logger.info(f"[START] Starting Enhanced Options Strategy Generation Agent in {trading_mode.upper()} mode...")

            self.is_running = True

            # Extract date parameters if provided (for training pipeline)
            from_date = kwargs.get('from_date')
            to_date = kwargs.get('to_date')

            if from_date and to_date:
                logger.info("[MODE] Training pipeline mode - generating comprehensive strategies")
                # Generate comprehensive strategies for training
                success = await self._generate_comprehensive_strategies(from_date, to_date)
                return success
            else:
                logger.info("[MODE] Live mode - generating real-time strategies")
                # Generate strategies for each underlying
                for underlying in self.config['underlying_symbols']:
                    await self._generate_strategies_for_underlying(underlying)

                # Save generated strategies
                await self._save_strategies()

                logger.info("[SUCCESS] Strategy generation completed")
                return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start agent: {e}")
            return False

    async def _generate_comprehensive_strategies(self, from_date: str, to_date: str) -> bool:
        """Generate comprehensive strategies for training pipeline"""
        try:
            logger.info("[COMPREHENSIVE] Starting comprehensive strategy generation...")

            # Load feature data for strategy generation
            for underlying in self.config['underlying_symbols']:
                logger.info(f"[COMPREHENSIVE] Processing {underlying} strategies...")

                # Load feature data for all timeframes
                for timeframe in ["1min", "3min", "5min", "15min"]:
                    logger.info(f"[COMPREHENSIVE] Generating {timeframe} strategies for {underlying}...")

                    # Load feature data using market data loader
                    feature_data = await self.market_data_loader.load_feature_data(underlying, timeframe)

                    if feature_data is None or feature_data.height == 0:
                        logger.warning(f"[WARNING] No feature data found for {underlying} {timeframe}")
                        continue

                    # Generate comprehensive strategies including deep OTM and intraday
                    strategies = await self._generate_comprehensive_strategy_set(
                        underlying, timeframe, feature_data
                    )

                    if strategies:
                        # Save strategies using strategy saver
                        await self.strategy_saver.save_comprehensive_strategies(strategies, underlying, timeframe)
                        logger.info(f"[SUCCESS] Generated {len(strategies)} strategies for {underlying} {timeframe}")

            logger.info("[SUCCESS] Comprehensive strategy generation completed")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Comprehensive strategy generation failed: {e}")
            return False
    
# Market analysis is now handled by the MarketAnalyzer class
    
    async def _generate_deep_otm_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                          market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate deep OTM strategies for extreme market moves"""
        try:
            strategies = []

            # Use strategy generators for deep OTM strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate deep OTM call strategies
                deep_otm_calls = await self.strategy_generators.generate_strategy_type(
                    StrategyType.DEEP_OTM_LONG_CALL, underlying, option_chain, spot_price
                )
                strategies.extend(deep_otm_calls)

                # Generate deep OTM put strategies
                deep_otm_puts = await self.strategy_generators.generate_strategy_type(
                    StrategyType.DEEP_OTM_LONG_PUT, underlying, option_chain, spot_price
                )
                strategies.extend(deep_otm_puts)

            logger.info(f"[DEEP_OTM] Generated {len(strategies)} deep OTM strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate deep OTM strategies: {e}")
            return []

    async def _generate_intraday_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                          market_conditions: Dict, timeframe: str) -> List[OptionsStrategy]:
        """Generate intraday-specific strategies"""
        try:
            strategies = []

            # Use strategy generators for intraday strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate various intraday strategies
                intraday_types = [
                    StrategyType.INTRADAY_SCALPING_CALL,
                    StrategyType.INTRADAY_SCALPING_PUT,
                    StrategyType.INTRADAY_MOMENTUM_CALL,
                    StrategyType.INTRADAY_MOMENTUM_PUT,
                    StrategyType.INTRADAY_REVERSAL_CALL,
                    StrategyType.INTRADAY_REVERSAL_PUT
                ]

                for strategy_type in intraday_types:
                    intraday_strategies = await self.strategy_generators.generate_strategy_type(
                        strategy_type, underlying, option_chain, spot_price
                    )
                    strategies.extend(intraday_strategies)

            logger.info(f"[INTRADAY] Generated {len(strategies)} intraday strategies for {underlying} {timeframe}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate intraday strategies: {e}")
            return []

    async def _generate_gamma_scalping_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                                market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate gamma scalping strategies"""
        try:
            strategies = []

            # Use strategy generators for gamma scalping strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate gamma scalping strategies
                gamma_types = [
                    StrategyType.GAMMA_SCALPING_LONG,
                    StrategyType.DELTA_NEUTRAL_GAMMA_SCALP
                ]

                for strategy_type in gamma_types:
                    gamma_strategies = await self.strategy_generators.generate_strategy_type(
                        strategy_type, underlying, option_chain, spot_price
                    )
                    strategies.extend(gamma_strategies)

            logger.info(f"[GAMMA] Generated {len(strategies)} gamma scalping strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate gamma scalping strategies: {e}")
            return []

    async def _generate_directional_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                             market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate basic directional strategies"""
        try:
            strategies = []

            # Use strategy generators for directional strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate basic directional strategies
                directional_types = [
                    StrategyType.LONG_CALL,
                    StrategyType.LONG_PUT,
                    StrategyType.PROTECTIVE_PUT
                ]

                for strategy_type in directional_types:
                    directional_strategies = await self.strategy_generators.generate_strategy_type(
                        strategy_type, underlying, option_chain, spot_price
                    )
                    strategies.extend(directional_strategies)

            logger.info(f"[DIRECTIONAL] Generated {len(strategies)} directional strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate directional strategies: {e}")
            return []

    async def _generate_atm_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                     market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate ATM-specific strategies"""
        try:
            strategies = []

            # Use strategy generators for ATM strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate ATM strategies
                atm_types = [
                    StrategyType.ATM_LONG_CALL,
                    StrategyType.ATM_LONG_PUT
                ]

                for strategy_type in atm_types:
                    atm_strategies = await self.strategy_generators.generate_strategy_type(
                        strategy_type, underlying, option_chain, spot_price
                    )
                    strategies.extend(atm_strategies)

            logger.info(f"[ATM] Generated {len(strategies)} ATM strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM strategies: {e}")
            return []

    async def _generate_otm_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                     market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate OTM-specific strategies"""
        try:
            strategies = []

            # Use strategy generators for OTM strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate OTM strategies
                otm_types = [
                    StrategyType.OTM_LONG_CALL,
                    StrategyType.OTM_LONG_PUT
                ]

                for strategy_type in otm_types:
                    otm_strategies = await self.strategy_generators.generate_strategy_type(
                        strategy_type, underlying, option_chain, spot_price
                    )
                    strategies.extend(otm_strategies)

            logger.info(f"[OTM] Generated {len(strategies)} OTM strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate OTM strategies: {e}")
            return []

    async def _generate_far_otm_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                         market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate Far OTM strategies for high probability trades"""
        try:
            strategies = []

            # Use strategy generators for Far OTM strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate Far OTM strategies
                far_otm_types = [
                    StrategyType.FAR_OTM_LONG_CALL,
                    StrategyType.FAR_OTM_LONG_PUT
                ]

                for strategy_type in far_otm_types:
                    far_otm_strategies = await self.strategy_generators.generate_strategy_type(
                        strategy_type, underlying, option_chain, spot_price
                    )
                    strategies.extend(far_otm_strategies)

            logger.info(f"[FAR_OTM] Generated {len(strategies)} Far OTM strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate Far OTM strategies: {e}")
            return []

    async def _generate_volatility_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                            market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate volatility-based strategies"""
        try:
            strategies = []

            # Use strategy generators for volatility strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate volatility strategies
                volatility_types = [
                    StrategyType.LONG_STRADDLE,
                    StrategyType.LONG_STRANGLE,
                    StrategyType.VOLATILITY_BREAKOUT_LONG,
                    StrategyType.VIX_BASED_STRATEGY
                ]

                for strategy_type in volatility_types:
                    volatility_strategies = await self.strategy_generators.generate_strategy_type(
                        strategy_type, underlying, option_chain, spot_price
                    )
                    strategies.extend(volatility_strategies)

            logger.info(f"[VOLATILITY] Generated {len(strategies)} volatility strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate volatility strategies: {e}")
            return []

    async def _generate_spread_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                        market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate spread strategies"""
        try:
            strategies = []

            # Use strategy generators for spread strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate spread strategies
                spread_types = [
                    StrategyType.BULL_CALL_SPREAD,
                    StrategyType.BEAR_PUT_SPREAD,
                    StrategyType.CALL_CALENDAR_SPREAD,
                    StrategyType.PUT_CALENDAR_SPREAD
                ]

                for strategy_type in spread_types:
                    spread_strategies = await self.strategy_generators.generate_strategy_type(
                        strategy_type, underlying, option_chain, spot_price
                    )
                    strategies.extend(spread_strategies)

            logger.info(f"[SPREAD] Generated {len(strategies)} spread strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate spread strategies: {e}")
            return []

    async def _generate_weekly_expiry_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                               market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate weekly expiry specific strategies"""
        try:
            strategies = []

            # Use strategy generators for weekly expiry strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate weekly expiry strategies
                weekly_types = [
                    StrategyType.WEEKLY_EXPIRY_STRADDLE,
                    StrategyType.MONTHLY_EXPIRY_STRANGLE
                ]

                for strategy_type in weekly_types:
                    weekly_strategies = await self.strategy_generators.generate_strategy_type(
                        strategy_type, underlying, option_chain, spot_price
                    )
                    strategies.extend(weekly_strategies)

            logger.info(f"[WEEKLY] Generated {len(strategies)} weekly expiry strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate weekly expiry strategies: {e}")
            return []

    # Market analysis is now handled by MarketAnalyzer class
    # Strategy saving is now handled by StrategySaver class

    async def _generate_regime_specific_strategies(self, underlying: str, feature_data: pl.DataFrame,
                                                 market_conditions: Dict) -> List[OptionsStrategy]:
        """Generate market regime specific strategies"""
        try:
            strategies = []

            # Use strategy generators for regime-specific strategies
            if underlying in self.market_data_cache:
                option_chain = self.market_data_cache[underlying]
                spot_price = self._estimate_spot_price(option_chain)

                # Generate regime-specific strategies based on market conditions
                regime_types = []

                # Add strategies based on volatility regime
                if market_conditions.get('volatility_regime') == 'high':
                    regime_types.extend([
                        StrategyType.LONG_STRADDLE,
                        StrategyType.LONG_STRANGLE
                    ])
                elif market_conditions.get('volatility_regime') == 'low':
                    regime_types.extend([
                        StrategyType.BUTTERFLY_SPREAD,
                        StrategyType.CONDOR_SPREAD
                    ])

                # Add strategies based on market phase
                if market_conditions.get('market_phase') == 'trending':
                    regime_types.extend([
                        StrategyType.BULL_CALL_SPREAD,
                        StrategyType.BEAR_PUT_SPREAD
                    ])

                for strategy_type in regime_types:
                    regime_strategies = await self.strategy_generators.generate_strategy_type(
                        strategy_type, underlying, option_chain, spot_price
                    )
                    strategies.extend(regime_strategies)

            logger.info(f"[REGIME] Generated {len(strategies)} regime-specific strategies for {underlying}")
            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate regime-specific strategies: {e}")
            return []
    
    async def _generate_strategies_for_underlying(self, underlying: str):
        """Generate strategies for specific underlying"""
        try:
            logger.info(f"[GENERATE] Generating strategies for {underlying}...")

            if underlying not in self.market_data_cache:
                logger.warning(f"[WARNING] No market data for {underlying}")
                return

            option_chain = self.market_data_cache[underlying]

            # Get current spot price (estimated from ATM options)
            spot_price = self._estimate_spot_price(option_chain)

            strategies = []

            # Generate each strategy type using strategy generators
            for strategy_type in self.config['strategy_types']:
                strategy_list = await self.strategy_generators.generate_strategy_type(
                    strategy_type, underlying, option_chain, spot_price
                )
                strategies.extend(strategy_list)

            # Filter and optimize strategies using strategy optimizer
            filtered_strategies = await self.strategy_optimizer.filter_strategies(strategies)
            optimized_strategies = await self.strategy_optimizer.optimize_strategies(filtered_strategies)

            # Store strategies
            self.generated_strategies[underlying] = optimized_strategies

            logger.info(f"[SUCCESS] Generated {len(optimized_strategies)} strategies for {underlying}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate strategies for {underlying}: {e}")
    
    def _estimate_spot_price(self, option_chain: pl.DataFrame) -> float:
        """Estimate current spot price from option chain"""
        try:
            # Find ATM options (closest to spot)
            strikes = option_chain['strike_price'].unique().sort().to_list()

            if not strikes:
                return 25000.0  # Default for Nifty

            # Use middle strike as approximation
            mid_strike = strikes[len(strikes) // 2]
            return mid_strike

        except Exception as e:
            logger.warning(f"[WARNING] Failed to estimate spot price: {e}")
            return 25000.0

    def _get_atm_strikes(self, option_chain: pl.DataFrame, spot_price: float) -> List[float]:
        """Get ATM strike prices within threshold"""
        try:
            strikes = option_chain['strike_price'].unique().sort().to_list()
            atm_threshold = self.config['atm_threshold']

            atm_strikes = []
            for strike in strikes:
                if abs(strike - spot_price) / spot_price <= atm_threshold:
                    atm_strikes.append(strike)

            return atm_strikes if atm_strikes else [min(strikes, key=lambda x: abs(x - spot_price))]

        except Exception as e:
            logger.warning(f"[WARNING] Failed to get ATM strikes: {e}")
            return [spot_price]

    def _get_otm_strikes(self, option_chain: pl.DataFrame, spot_price: float, option_type: str) -> List[float]:
        """Get OTM strike prices"""
        try:
            strikes = option_chain['strike_price'].unique().sort().to_list()
            otm_threshold = self.config['otm_threshold']

            otm_strikes = []
            for strike in strikes:
                if option_type == 'CE' and strike > spot_price:
                    # OTM calls are above spot
                    if (strike - spot_price) / spot_price <= otm_threshold:
                        otm_strikes.append(strike)
                elif option_type == 'PE' and strike < spot_price:
                    # OTM puts are below spot
                    if (spot_price - strike) / spot_price <= otm_threshold:
                        otm_strikes.append(strike)

            return otm_strikes

        except Exception as e:
            logger.warning(f"[WARNING] Failed to get OTM strikes: {e}")
            return []

    # Helper methods for strike price calculations are now handled by StrategyGenerators
    # Strategy type generation is now handled by StrategyGenerators class
    # All individual strategy generation methods are now handled by StrategyGenerators class

    # All old strategy generation methods removed - now handled by StrategyGenerators class

    # Old strategy generation methods removed - now handled by StrategyGenerators class

    async def _save_strategies(self):
        """Save generated strategies using StrategySaver"""
        try:
            logger.info("[SAVE] Saving generated strategies...")

            all_strategies = []
            for underlying, strategies in self.generated_strategies.items():
                all_strategies.extend(strategies)

            if all_strategies:
                # Use strategy saver to save strategies
                await self.strategy_saver.save_strategies(all_strategies)
                logger.info(f"[SUCCESS] Saved {len(all_strategies)} strategies")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save strategies: {e}")

    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Strategy Generation Agent...")
            self.is_running = False
            self.generated_strategies.clear()
            self.market_data_cache.clear()
            logger.info("[SUCCESS] Options Strategy Generation Agent cleaned up")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup agent: {e}")


if __name__ == "__main__":
    # Demo usage
    import asyncio

    async def main():
        agent = OptionsStrategyGenerationAgent()
        await agent.initialize()
        await agent.start()

    asyncio.run(main())
    
    async def _generate_long_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, 
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate long straddle strategies"""
        try:
            strategies = []
            
            # Get unique strikes
            strikes = option_chain['strike_price'].unique().sort().to_list()
            
            for strike in strikes:
                # Get call and put at same strike
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') & 
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') & 
                    (pl.col('strike_price') == strike)
                ).height > 0 else None
                
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') & 
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') & 
                    (pl.col('strike_price') == strike)
                ).height > 0 else None
                
                if not call or not put:
                    continue
                
                # Create legs
                call_leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=call['ltp'],
                    underlying=underlying
                )
                
                put_leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=put['ltp'],
                    underlying=underlying
                )
                
                # Calculate strategy metrics
                total_premium = call['ltp'] + put['ltp']
                max_profit = float('inf')  # Unlimited if big move
                max_loss = total_premium
                break_even_upper = strike + total_premium
                break_even_lower = strike - total_premium
                
                # Estimate probability of profit (move beyond break-evens)
                prob_profit = 0.4  # Simplified estimate for volatility strategy
                
                strategy = OptionsStrategy(
                    strategy_id=f"LS_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.LONG_STRADDLE,
                    underlying=underlying,
                    legs=[call_leg, put_leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even_lower, break_even_upper],
                    probability_of_profit=prob_profit,
                    net_premium=-total_premium,
                    margin_required=total_premium,
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "IV", "operator": "low"}], # Placeholder
                    exit_conditions=[{"indicator": "IV", "operator": "high"}] # Placeholder
                )
                
                strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long straddle strategies: {e}")
            return []
    
    async def _generate_long_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate long strangle strategies"""
        try:
            strategies = []

            # Get OTM strikes for calls and puts
            otm_call_strikes = self._get_otm_strikes(option_chain, spot_price, 'CE')
            otm_put_strikes = self._get_otm_strikes(option_chain, spot_price, 'PE')

            for call_strike in otm_call_strikes[:3]:  # Limit to top 3
                for put_strike in otm_put_strikes[:3]:
                    if call_strike <= put_strike:
                        continue

                    # Get call and put options
                    call = option_chain.filter(
                        (pl.col('option_type') == 'CE') &
                        (pl.col('strike_price') == call_strike)
                    ).row(0, named=True) if option_chain.filter(
                        (pl.col('option_type') == 'CE') &
                        (pl.col('strike_price') == call_strike)
                    ).height > 0 else None

                    put = option_chain.filter(
                        (pl.col('option_type') == 'PE') &
                        (pl.col('strike_price') == put_strike)
                    ).row(0, named=True) if option_chain.filter(
                        (pl.col('option_type') == 'PE') &
                        (pl.col('strike_price') == put_strike)
                    ).height > 0 else None

                    if not call or not put:
                        continue

                    # Create legs
                    call_leg = OptionsLeg(
                        symbol=call['symbol'],
                        option_type='CE',
                        strike_price=call_strike,
                        expiry_date='2024-01-25',
                        quantity=1,
                        premium=call['ltp'],
                        underlying=underlying
                    )

                    put_leg = OptionsLeg(
                        symbol=put['symbol'],
                        option_type='PE',
                        strike_price=put_strike,
                        expiry_date='2024-01-25',
                        quantity=1,
                        premium=put['ltp'],
                        underlying=underlying
                    )

                    # Calculate strategy metrics
                    total_premium = call['ltp'] + put['ltp']
                    max_profit = float('inf')  # Unlimited if big move
                    max_loss = total_premium
                    break_even_upper = call_strike + total_premium
                    break_even_lower = put_strike - total_premium

                    # Estimate probability of profit
                    prob_profit = 0.35  # Lower than straddle due to wider strikes

                    strategy = OptionsStrategy(
                        strategy_id=f"LST_{underlying}_{call_strike}_{put_strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                        strategy_type=StrategyType.LONG_STRANGLE,
                        underlying=underlying,
                        legs=[call_leg, put_leg],
                        max_profit=max_profit,
                        max_loss=max_loss,
                        break_even_points=[break_even_lower, break_even_upper],
                        probability_of_profit=prob_profit,
                        net_premium=-total_premium,
                        margin_required=total_premium,
                        risk_reward_ratio=float('inf'),
                        target_profit=max_loss * 2,
                        stop_loss=max_loss * 0.5,
                        created_at=datetime.now(),
                        entry_conditions=[{"indicator": "IV", "operator": "low"}], # Placeholder
                        exit_conditions=[{"indicator": "IV", "operator": "high"}] # Placeholder
                    )

                    strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate long strangle strategies: {e}")
            return []
    
    # ATM Strategy Methods
    async def _generate_atm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long call strategies"""
        try:
            strategies = []
            atm_strikes = self._get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not call:
                    continue

                leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=call['ltp'],
                    underlying=underlying
                )

                max_profit = float('inf')
                max_loss = call['ltp']
                break_even = strike + call['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMLC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_LONG_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-call['ltp'],
                    margin_required=call['ltp'],
                    risk_reward_ratio=float('inf'),
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": ">", "value": 10000}], # Placeholder
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM long call strategies: {e}")
            return []

    async def _generate_atm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                              spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM long put strategies"""
        try:
            strategies = []
            atm_strikes = self._get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not put:
                    continue

                leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=1,
                    premium=put['ltp'],
                    underlying=underlying
                )

                max_profit = strike - put['ltp']
                max_loss = put['ltp']
                break_even = strike - put['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMLP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_LONG_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=-put['ltp'],
                    margin_required=put['ltp'],
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_loss * 2,
                    stop_loss=max_loss * 0.5,
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Volume", "operator": ">", "value": 10000}], # Placeholder
                    exit_conditions=[{"indicator": "Time", "operator": "end_of_day"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM long put strategies: {e}")
            return []

    async def _generate_atm_short_call_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM short call strategies"""
        try:
            strategies = []
            atm_strikes = self._get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                call = option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'CE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not call:
                    continue

                leg = OptionsLeg(
                    symbol=call['symbol'],
                    option_type='CE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=-1,  # Short position
                    premium=call['ltp'],
                    underlying=underlying
                )

                max_profit = call['ltp']
                max_loss = float('inf')  # Unlimited loss
                break_even = strike + call['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMSC_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_SHORT_CALL,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=call['ltp'],  # Positive because we receive premium
                    margin_required=call['ltp'] * 10,  # Estimated margin
                    risk_reward_ratio=0,  # Unlimited loss
                    target_profit=max_profit * 0.8,  # 80% of max profit
                    stop_loss=max_profit * 2,  # 200% of premium received
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Delta", "operator": "near_zero"}], # Placeholder
                    exit_conditions=[{"indicator": "Delta", "operator": "far_from_zero"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM short call strategies: {e}")
            return []

    async def _generate_atm_short_put_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                               spot_price: float) -> List[OptionsStrategy]:
        """Generate ATM short put strategies"""
        try:
            strategies = []
            atm_strikes = self._get_atm_strikes(option_chain, spot_price)

            for strike in atm_strikes:
                put = option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).row(0, named=True) if option_chain.filter(
                    (pl.col('option_type') == 'PE') &
                    (pl.col('strike_price') == strike)
                ).height > 0 else None

                if not put:
                    continue

                leg = OptionsLeg(
                    symbol=put['symbol'],
                    option_type='PE',
                    strike_price=strike,
                    expiry_date='2024-01-25',
                    quantity=-1,  # Short position
                    premium=put['ltp'],
                    underlying=underlying
                )

                max_profit = put['ltp']
                max_loss = strike - put['ltp']
                break_even = strike - put['ltp']
                prob_profit = 0.5  # ATM has 50% probability

                strategy = OptionsStrategy(
                    strategy_id=f"ATMSP_{underlying}_{strike}_{datetime.now().strftime('%Y%m%d%H%M%S')}",
                    strategy_type=StrategyType.ATM_SHORT_PUT,
                    underlying=underlying,
                    legs=[leg],
                    max_profit=max_profit,
                    max_loss=max_loss,
                    break_even_points=[break_even],
                    probability_of_profit=prob_profit,
                    net_premium=put['ltp'],  # Positive because we receive premium
                    margin_required=put['ltp'] * 10,  # Estimated margin
                    risk_reward_ratio=max_profit / max_loss if max_loss > 0 else 0,
                    target_profit=max_profit * 0.8,  # 80% of max profit
                    stop_loss=max_profit * 2,  # 200% of premium received
                    created_at=datetime.now(),
                    entry_conditions=[{"indicator": "Delta", "operator": "near_zero"}], # Placeholder
                    exit_conditions=[{"indicator": "Delta", "operator": "far_from_zero"}] # Placeholder
                )

                strategies.append(strategy)

            return strategies

        except Exception as e:
            logger.error(f"[ERROR] Failed to generate ATM short put strategies: {e}")
            return []

    async def _generate_bull_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                  spot_price: float) -> List[OptionsStrategy]:
        """Generate bull call spread strategies"""
        # Buy lower strike call, sell higher strike call
        # Implementation would create two-leg spread strategies
        return []
    
    async def _generate_bear_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                                 spot_price: float) -> List[OptionsStrategy]:
        """Generate bear put spread strategies"""
        # Buy higher strike put, sell lower strike put
        # Implementation would create two-leg spread strategies
        return []

    async def _generate_iron_condor_strategies(self, underlying: str, option_chain: pl.DataFrame,
                                             spot_price: float) -> List[OptionsStrategy]:
        """Generate iron condor strategies"""
        # Four-leg strategy: sell call spread + sell put spread
        # Implementation would create four-leg strategies
        return []

    # Placeholder methods for remaining strategies
    async def _generate_short_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate short call strategies"""
        return []

    async def _generate_short_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate short put strategies"""
        return []

    async def _generate_covered_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate covered call strategies"""
        return []

    async def _generate_protective_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate protective put strategies"""
        return []

    async def _generate_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM long call strategies"""
        return []

    async def _generate_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM long put strategies"""
        return []

    async def _generate_otm_short_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM short call strategies"""
        return []

    async def _generate_otm_short_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate OTM short put strategies"""
        return []

    async def _generate_far_otm_long_call_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Far OTM long call strategies"""
        return []

    async def _generate_far_otm_long_put_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate Far OTM long put strategies"""
        return []

    async def _generate_short_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate short straddle strategies"""
        return []

    async def _generate_short_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate short strangle strategies"""
        return []

    async def _generate_iron_butterfly_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate iron butterfly strategies"""
        return []

    async def _generate_bear_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate bear call spread strategies"""
        return []

    async def _generate_bull_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate bull put spread strategies"""
        return []

    async def _generate_call_calendar_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate call calendar spread strategies"""
        return []

    async def _generate_put_calendar_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate put calendar spread strategies"""
        return []

    async def _generate_ratio_call_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ratio call spread strategies"""
        return []

    async def _generate_ratio_put_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate ratio put spread strategies"""
        return []

    async def _generate_collar_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate collar strategies"""
        return []

    async def _generate_butterfly_spread_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate butterfly spread strategies"""
        return []

    async def _generate_jade_lizard_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate jade lizard strategies"""
        return []

    async def _generate_synthetic_long_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate synthetic long strategies"""
        return []

    async def _generate_weekly_expiry_straddle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate weekly expiry straddle strategies"""
        return []

    async def _generate_monthly_expiry_strangle_strategies(self, underlying: str, option_chain: pl.DataFrame, spot_price: float) -> List[OptionsStrategy]:
        """Generate monthly expiry strangle strategies"""
        return []
    
    async def _filter_strategies(self, strategies: List[OptionsStrategy]) -> List[OptionsStrategy]:
        """Filter strategies based on criteria"""
        try:
            filtered = []
            
            for strategy in strategies:
                # Apply filters
                if (strategy.probability_of_profit >= self.config['min_probability_of_profit'] and
                    strategy.risk_reward_ratio >= self.config['min_risk_reward_ratio'] and
                    strategy.max_loss <= 10000):  # Max loss limit
                    
                    filtered.append(strategy)
            
            logger.info(f"[FILTER] Filtered {len(filtered)} strategies from {len(strategies)}")
            return filtered
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to filter strategies: {e}")
            return strategies
    
    async def _optimize_strategies(self, strategies: List[OptionsStrategy]) -> List[OptionsStrategy]:
        """Optimize strategy parameters"""
        try:
            # Sort by risk-reward ratio and probability of profit
            optimized = sorted(
                strategies,
                key=lambda s: (s.risk_reward_ratio * s.probability_of_profit),
                reverse=True
            )
            
            # Limit number of strategies per underlying
            max_strategies = self.config['max_strategies_per_underlying']
            optimized = optimized[:max_strategies]
            
            logger.info(f"[OPTIMIZE] Optimized to {len(optimized)} strategies")
            return optimized
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to optimize strategies: {e}")
            return strategies
    
    async def _save_strategies(self):
        """Save generated strategies"""
        try:
            logger.info("[SAVE] Saving generated strategies...")
            
            all_strategies = []
            
            for underlying, strategies in self.generated_strategies.items():
                for strategy in strategies:
                    all_strategies.append(strategy.to_dict())
            
            if all_strategies:
                # Convert to DataFrame and save
                strategies_df = pl.DataFrame(all_strategies)
                
                filename = f"generated_strategies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.parquet"
                filepath = self.strategies_path / filename
                
                strategies_df.write_parquet(filepath)
                
                # Also save as JSON for readability
                json_filename = f"generated_strategies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                json_filepath = self.strategies_path / json_filename
                
                async with aiofiles.open(json_filepath, 'w') as f:
                    await f.write(json.dumps(all_strategies, indent=2, default=str))
                
                logger.info(f"[SUCCESS] Saved {len(all_strategies)} strategies")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save strategies: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Strategy Generation Agent...")
            self.is_running = False
            logger.info("[SUCCESS] Options Strategy Generation Agent cleaned up")
            
        except Exception as e:
            logger.error(f"[ERROR] Cleanup failed: {e}")

# Example usage
async def main():
    """Example usage of Options Strategy Generation Agent"""
    agent = OptionsStrategyGenerationAgent()
    
    try:
        await agent.initialize()
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Agent interrupted by user")
    finally:
        await agent.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
#!/usr/bin/env python3
"""
Options Strategy Generation Agent - Dynamic Strategy Creation & Optimization

Features:
📊 1. Strategy Generation
- Directional strategies (Long Call/Put, Covered Call)
- Volatility strategies (Straddle, Strangle, Iron Condor)
- Spread strategies (Bull/Bear spreads, Calendar spreads)
- Complex multi-leg strategies

📈 2. Dynamic Optimization
- Real-time strategy parameter tuning
- Market regime-based strategy selection
- Risk-adjusted strategy optimization
- Greeks-based strategy construction

⚡ 3. Strategy Validation
- Risk-reward analysis
- Probability of profit calculations
- Maximum loss/profit scenarios
- Break-even point analysis

🎯 4. Performance Optimization
- Vectorized strategy calculations
- Polars + PyArrow for fast processing
- Parallel strategy evaluation
- Memory-efficient strategy storage
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import aiofiles
from dataclasses import dataclass, asdict
import json
from enum import Enum
import itertools

# Options pricing
try:
    import py_vollib.black_scholes as bs
    import py_vollib.black_scholes.greeks.analytical as greeks
except ImportError:
    logger.warning("py_vollib not installed. Using fallback calculations.")

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Comprehensive options strategy types for Indian market"""

    # Basic Directional Strategies
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"
    SHORT_CALL = "short_call"
    SHORT_PUT = "short_put"
    COVERED_CALL = "covered_call"
    PROTECTIVE_PUT = "protective_put"
    CASH_SECURED_PUT = "cash_secured_put"

    # ATM Strategies
    ATM_LONG_CALL = "atm_long_call"
    ATM_LONG_PUT = "atm_long_put"
    ATM_SHORT_CALL = "atm_short_call"
    ATM_SHORT_PUT = "atm_short_put"

    # OTM Strategies
    OTM_LONG_CALL = "otm_long_call"
    OTM_LONG_PUT = "otm_long_put"
    OTM_SHORT_CALL = "otm_short_call"
    OTM_SHORT_PUT = "otm_short_put"

    # Far OTM Strategies
    FAR_OTM_LONG_CALL = "far_otm_long_call"
    FAR_OTM_LONG_PUT = "far_otm_long_put"
    FAR_OTM_SHORT_CALL = "far_otm_short_call"
    FAR_OTM_SHORT_PUT = "far_otm_short_put"

    # Volatility Strategies
    LONG_STRADDLE = "long_straddle"
    SHORT_STRADDLE = "short_straddle"
    LONG_STRANGLE = "long_strangle"
    SHORT_STRANGLE = "short_strangle"
    IRON_CONDOR = "iron_condor"
    IRON_BUTTERFLY = "iron_butterfly"
    REVERSE_IRON_CONDOR = "reverse_iron_condor"
    REVERSE_IRON_BUTTERFLY = "reverse_iron_butterfly"

    # Spread Strategies
    BULL_CALL_SPREAD = "bull_call_spread"
    BEAR_CALL_SPREAD = "bear_call_spread"
    BULL_PUT_SPREAD = "bull_put_spread"
    BEAR_PUT_SPREAD = "bear_put_spread"
    CALL_CALENDAR_SPREAD = "call_calendar_spread"
    PUT_CALENDAR_SPREAD = "put_calendar_spread"
    DIAGONAL_CALL_SPREAD = "diagonal_call_spread"
    DIAGONAL_PUT_SPREAD = "diagonal_put_spread"

    # Ratio Strategies
    RATIO_CALL_SPREAD = "ratio_call_spread"
    RATIO_PUT_SPREAD = "ratio_put_spread"
    RATIO_CALL_BACKSPREAD = "ratio_call_backspread"
    RATIO_PUT_BACKSPREAD = "ratio_put_backspread"

    # Collar Strategies
    COLLAR = "collar"
    REVERSE_COLLAR = "reverse_collar"

    # Synthetic Strategies
    SYNTHETIC_LONG = "synthetic_long"
    SYNTHETIC_SHORT = "synthetic_short"
    SYNTHETIC_CALL = "synthetic_call"
    SYNTHETIC_PUT = "synthetic_put"

    # Complex Multi-leg Strategies
    JADE_LIZARD = "jade_lizard"
    REVERSE_JADE_LIZARD = "reverse_jade_lizard"
    BUTTERFLY_SPREAD = "butterfly_spread"
    CONDOR_SPREAD = "condor_spread"
    CHRISTMAS_TREE = "christmas_tree"

    # Indian Market Specific
    WEEKLY_EXPIRY_STRADDLE = "weekly_expiry_straddle"
    MONTHLY_EXPIRY_STRANGLE = "monthly_expiry_strangle"
    NIFTY_IRON_CONDOR = "nifty_iron_condor"
    BANKNIFTY_BUTTERFLY = "banknifty_butterfly"

@dataclass
class OptionsLeg:
    """Single options leg in a strategy"""
    symbol: str
    option_type: str  # CE or PE
    strike_price: float
    expiry_date: str
    quantity: int  # Positive for long, negative for short
    premium: float
    underlying: str

@dataclass
class OptionsStrategy:
    """Complete options strategy definition"""
    strategy_id: str
    strategy_type: StrategyType
    underlying: str
    legs: List[OptionsLeg]
    max_profit: float
    max_loss: float
    break_even_points: List[float]
    probability_of_profit: float
    net_premium: float
    margin_required: float
    risk_reward_ratio: float
    target_profit: float
    stop_loss: float
    created_at: datetime
    entry_conditions: List[Dict]
    exit_conditions: List[Dict]
    
    def to_dict(self) -> Dict:
        """Convert strategy to dictionary"""
        return {
            'strategy_id': self.strategy_id,
            'strategy_type': self.strategy_type.value,
            'underlying': self.underlying,
            'legs': [asdict(leg) for leg in self.legs],
            'max_profit': self.max_profit,
            'max_loss': self.max_loss,
            'break_even_points': self.break_even_points,
            'probability_of_profit': self.probability_of_profit,
            'net_premium': self.net_premium,
            'margin_required': self.margin_required,
            'risk_reward_ratio': self.risk_reward_ratio,
            'target_profit': self.target_profit,
            'stop_loss': self.stop_loss,
            'created_at': self.created_at.isoformat(),
            'entry_conditions': self.entry_conditions,
            'exit_conditions': self.exit_conditions
        }

#!/usr/bin/env python3
"""
Options Strategy Generation Agent - Dynamic Strategy Creation & Optimization

Features:
📊 1. Strategy Generation
- Directional strategies (Long Call/Put, Covered Call)
- Volatility strategies (Straddle, Strangle, Iron Condor)
- Spread strategies (Bull/Bear spreads, Calendar spreads)
- Complex multi-leg strategies

📈 2. Dynamic Optimization
- Real-time strategy parameter tuning
- Market regime-based strategy selection
- Risk-adjusted strategy optimization
- Greeks-based strategy construction

⚡ 3. Strategy Validation
- Risk-reward analysis
- Probability of profit calculations
- Maximum loss/profit scenarios
- Break-even point analysis

🎯 4. Performance Optimization
- Vectorized strategy calculations
- Polars + PyArrow for fast processing
- Parallel strategy evaluation
- Memory-efficient strategy storage
"""

import asyncio
import logging
import polars as pl
import pyarrow as pa
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import aiofiles
from dataclasses import dataclass, asdict
import json
from enum import Enum
import itertools

# Options pricing
try:
    import py_vollib.black_scholes as bs
    import py_vollib.black_scholes.greeks.analytical as greeks
except ImportError:
    logger.warning("py_vollib not installed. Using fallback calculations.")

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Comprehensive options strategy types for Indian market"""

    # Basic Directional Strategies
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"
    SHORT_CALL = "short_call"
    SHORT_PUT = "short_put"
    COVERED_CALL = "covered_call"
    PROTECTIVE_PUT = "protective_put"
    CASH_SECURED_PUT = "cash_secured_put"

    # ATM Strategies
    ATM_LONG_CALL = "atm_long_call"
    ATM_LONG_PUT = "atm_long_put"
    ATM_SHORT_CALL = "atm_short_call"
    ATM_SHORT_PUT = "atm_short_put"

    # OTM Strategies
    OTM_LONG_CALL = "otm_long_call"
    OTM_LONG_PUT = "otm_long_put"
    OTM_SHORT_CALL = "otm_short_call"
    OTM_SHORT_PUT = "otm_short_put"

    # Far OTM Strategies (Deep OTM)
    FAR_OTM_LONG_CALL = "far_otm_long_call"
    FAR_OTM_LONG_PUT = "far_otm_long_put"
    FAR_OTM_SHORT_CALL = "far_otm_short_call"
    FAR_OTM_SHORT_PUT = "far_otm_short_put"
    
    # Deep OTM Strategies (Very Far OTM)
    DEEP_OTM_LONG_CALL = "deep_otm_long_call"
    DEEP_OTM_LONG_PUT = "deep_otm_long_put"
    DEEP_OTM_SHORT_CALL = "deep_otm_short_call"
    DEEP_OTM_SHORT_PUT = "deep_otm_short_put"
    
    # Intraday Strategies
    INTRADAY_SCALPING_CALL = "intraday_scalping_call"
    INTRADAY_SCALPING_PUT = "intraday_scalping_put"
    INTRADAY_MOMENTUM_CALL = "intraday_momentum_call"
    INTRADAY_MOMENTUM_PUT = "intraday_momentum_put"
    INTRADAY_REVERSAL_CALL = "intraday_reversal_call"
    INTRADAY_REVERSAL_PUT = "intraday_reversal_put"
    
    # Gamma Scalping Strategies
    GAMMA_SCALPING_LONG = "gamma_scalping_long"
    GAMMA_SCALPING_SHORT = "gamma_scalping_short"
    DELTA_NEUTRAL_GAMMA_SCALP = "delta_neutral_gamma_scalp"
    
    # Volatility Breakout Strategies
    VOLATILITY_BREAKOUT_LONG = "volatility_breakout_long"
    VOLATILITY_BREAKOUT_SHORT = "volatility_breakout_short"
    VIX_BASED_STRATEGY = "vix_based_strategy"

    # Volatility Strategies
    LONG_STRADDLE = "long_straddle"
    SHORT_STRADDLE = "short_straddle"
    LONG_STRANGLE = "long_strangle"
    SHORT_STRANGLE = "short_strangle"
    IRON_CONDOR = "iron_condor"
    IRON_BUTTERFLY = "iron_butterfly"
    REVERSE_IRON_CONDOR = "reverse_iron_condor"
    REVERSE_IRON_BUTTERFLY = "reverse_iron_butterfly"

    # Spread Strategies
    BULL_CALL_SPREAD = "bull_call_spread"
    BEAR_CALL_SPREAD = "bear_call_spread"
    BULL_PUT_SPREAD = "bull_put_spread"
    BEAR_PUT_SPREAD = "bear_put_spread"
    CALL_CALENDAR_SPREAD = "call_calendar_spread"
    PUT_CALENDAR_SPREAD = "put_calendar_spread"
    DIAGONAL_CALL_SPREAD = "diagonal_call_spread"
    DIAGONAL_PUT_SPREAD = "diagonal_put_spread"

    # Ratio Strategies
    RATIO_CALL_SPREAD = "ratio_call_spread"
    RATIO_PUT_SPREAD = "ratio_put_spread"
    RATIO_CALL_BACKSPREAD = "ratio_call_backspread"
    RATIO_PUT_BACKSPREAD = "ratio_put_backspread"

    # Collar Strategies
    COLLAR = "collar"
    REVERSE_COLLAR = "reverse_collar"

    # Synthetic Strategies
    SYNTHETIC_LONG = "synthetic_long"
    SYNTHETIC_SHORT = "synthetic_short"
    SYNTHETIC_CALL = "synthetic_call"
    SYNTHETIC_PUT = "synthetic_put"

    # Complex Multi-leg Strategies
    JADE_LIZARD = "jade_lizard"
    REVERSE_JADE_LIZARD = "reverse_jade_lizard"
    BUTTERFLY_SPREAD = "butterfly_spread"
    CONDOR_SPREAD = "condor_spread"
    CHRISTMAS_TREE = "christmas_tree"

    # Indian Market Specific
    WEEKLY_EXPIRY_STRADDLE = "weekly_expiry_straddle"
    MONTHLY_EXPIRY_STRANGLE = "monthly_expiry_strangle"
    NIFTY_IRON_CONDOR = "nifty_iron_condor"
    BANKNIFTY_BUTTERFLY = "banknifty_butterfly"

@dataclass
class OptionsLeg:
    """Single options leg in a strategy"""
    symbol: str
    option_type: str  # CE or PE
    strike_price: float
    expiry_date: str
    quantity: int  # Positive for long, negative for short
    premium: float
    underlying: str

@dataclass
class OptionsStrategy:
    """Complete options strategy definition"""
    strategy_id: str
    strategy_type: StrategyType
    underlying: str
    legs: List[OptionsLeg]
    max_profit: float
    max_loss: float
    break_even_points: List[float]
    probability_of_profit: float
    net_premium: float
    margin_required: float
    risk_reward_ratio: float
    target_profit: float
    stop_loss: float
    created_at: datetime
    
    def to_dict(self) -> Dict:
        """Convert strategy to dictionary"""
        return {
            'strategy_id': self.strategy_id,
            'strategy_type': self.strategy_type.value,
            'underlying': self.underlying,
            'legs': [asdict(leg) for leg in self.legs],
            'max_profit': self.max_profit,
            'max_loss': self.max_loss,
            'break_even_points': self.break_even_points,
            'probability_of_profit': self.probability_of_profit,
            'net_premium': self.net_premium,
            'margin_required': self.margin_required,
            'risk_reward_ratio': self.risk_reward_ratio,
            'target_profit': self.target_profit,
            'stop_loss': self.stop_loss,
            'created_at': self.created_at.isoformat()
        }

    async def cleanup(self):
        """Cleanup the agent"""
        try:
            logger.info("[CLEANUP] Cleaning up Options Strategy Generation Agent...")
            self.is_running = False
            self.generated_strategies.clear()
            self.market_data_cache.clear()
            logger.info("[SUCCESS] Options Strategy Generation Agent cleaned up")

        except Exception as e:
            logger.error(f"[ERROR] Failed to cleanup agent: {e}")


if __name__ == "__main__":
    # Demo usage
    import asyncio

    async def main():
        agent = OptionsStrategyGenerationAgent()
        await agent.initialize()
        await agent.start()

    asyncio.run(main())
