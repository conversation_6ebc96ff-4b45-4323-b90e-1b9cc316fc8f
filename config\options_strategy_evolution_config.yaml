# 🧬 Options Strategy Evolution Agent Configuration
# Comprehensive configuration for adaptive strategy optimization

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 GENETIC ALGORITHM PARAMETERS - EXTREME HIGH RESOURCE USAGE
# ═══════════════════════════════════════════════════════════════════════════════
genetic_algorithm:
  population_size: 2500            # MASSIVE increase from 150 - 16x more strategies for extreme diversity
  generations: 1000                # MASSIVE increase from 200 - 5x more generations
  mutation_rate: 0.35             # AGGRESSIVE increase from 0.20 - Much higher mutation rate
  crossover_rate: 0.95            # MAXIMUM crossover rate for extreme exploration
  selection_pressure: 0.15        # LOWER pressure for maximum diversity
  elite_percentage: 0.25          # HIGHER elite preservation for quality
  diversity_threshold: 0.4        # MUCH lower threshold for intensive testing

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 PERFORMANCE THRESHOLDS
# ═══════════════════════════════════════════════════════════════════════════════
performance_thresholds:
  min_roi: 0.02                   # 2% minimum ROI
  min_sharpe: 0.2                 # Minimum Sharpe ratio
  min_win_rate: 0.45              # 45% minimum win rate
  max_drawdown: 0.25              # 25% maximum drawdown
  min_trades: 20                  # Minimum trades for evaluation
  min_expectancy: 0.02            # Minimum expectancy
  min_profit_factor: 1.2          # Minimum profit factor
  min_calmar_ratio: 0.3           # Minimum Calmar ratio

# ═══════════════════════════════════════════════════════════════════════════════
# ⏰ EVOLUTION INTERVALS (seconds) - EXTREME SPEED INTERVALS
# ═══════════════════════════════════════════════════════════════════════════════
evolution_intervals:
  performance_check: 1            # 1 second - MAXIMUM frequency performance checks
  regime_adaptation: 3            # 3 seconds - EXTREME regime adaptation speed
  diversity_maintenance: 5        # 5 seconds - MAXIMUM diversity maintenance
  full_evolution: 10              # 10 seconds - EXTREME evolution cycle speed
  registry_cleanup: 30            # 30 seconds - RAPID cleanup
  experiment_evaluation: 2        # 2 seconds - MAXIMUM experiment evaluation speed
  learning_update: 15             # 15 seconds - RAPID learning updates

# ═══════════════════════════════════════════════════════════════════════════════
# 🧪 MUTATION PARAMETERS
# ═══════════════════════════════════════════════════════════════════════════════
mutation_parameters:
  # Technical indicator ranges
  rsi_range: [5, 25]              # RSI period range
  ma_range: [5, 50]               # Moving average period range
  bollinger_range: [10, 30]      # Bollinger bands period range
  
  # Risk management ranges
  stop_loss_range: [0.005, 0.05] # 0.5% to 5% stop loss
  take_profit_range: [0.01, 0.10] # 1% to 10% take profit
  position_size_range: [0.01, 0.1] # 1% to 10% position size
  
  # Options-specific ranges
  iv_rank_range: [10, 90]         # IV rank filter range
  delta_range: [0.1, 0.9]         # Delta range for options
  theta_range: [-0.1, 0.1]        # Theta range
  gamma_range: [0.01, 0.5]        # Gamma range
  
  # Time-based parameters
  timeframe_options:              # Available timeframes
    - "1min"
    - "3min"
    - "5min"
    - "15min"
    
  
  expiry_days_range: [7, 30]      # Days to expiry range
  
  # Market condition filters
  volume_multiplier_range: [0.8, 2.0] # Volume vs average
  volatility_range: [0.1, 0.5]   # Volatility range
  trend_strength_range: [0.3, 0.9] # Trend strength

# ═══════════════════════════════════════════════════════════════════════════════
# 🌊 MARKET REGIME DETECTION
# ═══════════════════════════════════════════════════════════════════════════════
market_regime:
  volatility_thresholds:
    low: 0.15                     # Below 15% volatility
    high: 0.25                    # Above 25% volatility
  
  trend_thresholds:
    weak: 0.3                     # Below 30% trend strength
    strong: 0.7                   # Above 70% trend strength
  
  volume_thresholds:
    low: 0.8                      # Below 80% of average
    high: 1.5                     # Above 150% of average
  
  regime_adaptation:
    trending_bull:
      preferred_strategies: ["long_call", "bull_call_spread"]
      mutation_bias: "aggressive_profit_targeting"
    
    trending_bear:
      preferred_strategies: ["long_put", "bear_put_spread"]
      mutation_bias: "defensive_risk_management"
    
    sideways_low_vol:
      preferred_strategies: ["iron_condor", "butterfly"]
      mutation_bias: "theta_optimization"
    
    sideways_high_vol:
      preferred_strategies: ["short_straddle", "short_strangle"]
      mutation_bias: "volatility_selling"
    
    volatile_uncertain:
      preferred_strategies: ["long_straddle", "long_strangle"]
      mutation_bias: "volatility_buying"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔬 EXPERIMENTATION FRAMEWORK - EXTREME THROUGHPUT TESTING
# ═══════════════════════════════════════════════════════════════════════════════
experimentation:
  max_concurrent_experiments: 500  # MASSIVE increase from 50 - 10x more concurrent experiments
  experiment_duration_days: 1     # MINIMUM duration - Maximum speed
  min_sample_size: 5              # MINIMUM sample size for maximum speed
  confidence_level: 0.80          # LOWER confidence for maximum speed
  
  a_b_testing:
    control_group_size: 0.4       # 40% control group - More resources for testing
    test_group_size: 0.6          # 60% test group - More aggressive testing
    early_stopping_threshold: 0.05 # Stop if 5% performance difference - Faster decisions
  
  experiment_types:
    - "parameter_optimization"
    - "regime_adaptation"
    - "risk_management_tuning"
    - "entry_timing_optimization"
    - "exit_strategy_testing"

# ═══════════════════════════════════════════════════════════════════════════════
# 🔄 SELF-LEARNING PARAMETERS
# ═══════════════════════════════════════════════════════════════════════════════
self_learning:
  learning_rate: 0.01             # Learning rate for parameter updates
  memory_window_days: 30          # Days of history to consider
  adaptation_threshold: 0.05      # Minimum improvement to adapt
  
  reinforcement_learning:
    enabled: true
    reward_function: "sharpe_weighted_return"
    exploration_rate: 0.1         # Epsilon for exploration
    discount_factor: 0.95         # Gamma for future rewards
  
  pattern_recognition:
    enabled: true
    min_pattern_occurrences: 5    # Minimum occurrences to recognize pattern
    pattern_confidence: 0.8       # Confidence threshold for patterns

# ═══════════════════════════════════════════════════════════════════════════════
# 📧 NOTIFICATION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
notifications:
  email_enabled: false            # Enable email notifications
  telegram_enabled: false        # Enable Telegram notifications
  
  email_config:
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: ""                  # Your email username
    password: ""                  # Your email password (use app password)
    recipients: []                # List of recipient emails
  
  telegram_config:
    bot_token: ""                 # Telegram bot token
    chat_ids: []                  # List of chat IDs to notify
  
  notification_triggers:
    strategy_promoted: true       # Notify when strategy is promoted
    strategy_demoted: true        # Notify when strategy is demoted
    regime_change: true           # Notify on market regime change
    evolution_completed: true     # Notify when evolution cycle completes
    performance_alert: true       # Notify on performance issues
    experiment_results: true      # Notify on experiment completion

# ═══════════════════════════════════════════════════════════════════════════════
# ⚡ PERFORMANCE SETTINGS - MAXIMUM RESOURCE UTILIZATION
# ═══════════════════════════════════════════════════════════════════════════════
performance:
  gpu_acceleration: true          # Enable GPU acceleration with CuPy if available
  cpu_intensive_mode: true        # Enable CPU-intensive processing mode
  memory_aggressive_mode: true    # Use aggressive memory allocation
  parallel_backtesting: true      # Run multiple backtests in parallel
  concurrent_strategy_limit: 100  # Maximum concurrent strategies being processed
  batch_processing_size: 50       # Process strategies in larger batches

  resource_targets:
    target_cpu_usage: 98          # MAXIMUM CPU usage target - 98%
    target_memory_usage_gb: 20    # MASSIVE memory target - 20GB
    max_memory_usage_gb: 25       # MAXIMUM memory limit - 25GB

  optimization_flags:
    enable_numba_jit: true        # Enable Numba JIT compilation
    enable_cython_extensions: true # Use Cython extensions if available
    vectorized_operations: true   # Prefer vectorized operations
    memory_pool_enabled: true     # Use memory pooling for frequent allocations
    aggressive_optimization: true # Enable all aggressive optimizations
    disable_safety_checks: true   # Disable safety checks for maximum speed
    force_parallel_execution: true # Force parallel execution everywhere

# 🗂️ STRATEGY REGISTRY SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
strategy_registry:
  max_versions_per_strategy: 10   # Maximum versions to keep
  archive_after_days: 90          # Archive strategies after 90 days
  cleanup_disabled_after_days: 30 # Remove disabled strategies after 30 days
  
  version_control:
    auto_versioning: true         # Automatic version numbering
    semantic_versioning: false    # Use semantic versioning (v1.2.3)
    branch_naming: "evolution"    # Branch name for evolution
  
  metadata_tracking:
    track_lineage: true           # Track strategy lineage
    track_performance: true       # Track performance metrics
    track_regime_performance: true # Track regime-specific performance

# ═══════════════════════════════════════════════════════════════════════════════
# 🪟 WINDOWS-SPECIFIC SETTINGS - HIGH RESOURCE UTILIZATION
# ═══════════════════════════════════════════════════════════════════════════════
windows_optimization:
  use_multiprocessing: true      # Use multiprocessing for parallel evolution
  max_workers: 64                # EXTREME workers - 4x increase for maximum CPU usage
  memory_limit_mb: 25600         # MASSIVE memory limit - 25GB for extreme memory usage
  cpu_affinity_enabled: true     # Enable CPU affinity for better performance
  priority_class: "realtime"     # MAXIMUM priority for the process

  file_handling:
    use_async_io: true            # Use async file I/O
    buffer_size: 65536            # Increased from 8192 - Larger buffer for better I/O performance
    temp_directory: "temp"        # Temporary directory for processing
    concurrent_file_ops: 8        # Number of concurrent file operations

  performance:
    enable_jit: true             # Enable JIT compilation (if available)
    optimize_polars: true         # Use Polars optimizations
    use_pyarrow_backend: true     # Use PyArrow backend for Polars
    enable_vectorization: true    # Enable vectorized operations
    parallel_strategy_evaluation: true # Evaluate strategies in parallel

# ═══════════════════════════════════════════════════════════════════════════════
# 📊 POLARS/PYARROW CONFIGURATION - HIGH PERFORMANCE DATA PROCESSING
# ═══════════════════════════════════════════════════════════════════════════════
data_processing:
  polars_config:
    streaming: true               # Enable streaming for large datasets
    lazy_evaluation: true         # Use lazy evaluation
    parallel_execution: true      # Enable parallel execution
    thread_pool_size: 16          # Increased thread pool for parallel operations
    memory_map: true              # Use memory mapping for large files

  pyarrow_config:
    memory_pool: "system"         # Memory pool type
    compression: "lz4"            # Changed from snappy - Faster compression for high throughput
    batch_size: 65536             # Larger batch size for better performance

  technical_indicators:
    use_polars_talib: true        # Use polars-talib if available
    fallback_to_custom: true      # Fallback to custom implementations
    cache_indicators: true        # Cache calculated indicators
    parallel_indicator_calc: true # Calculate indicators in parallel
    indicator_batch_size: 10000   # Batch size for indicator calculations

# ═══════════════════════════════════════════════════════════════════════════════
# 🎯 ENSEMBLE STRATEGY SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
ensemble_strategies:
  enabled: true                   # Enable ensemble strategy creation
  min_strategies_for_ensemble: 3  # Minimum strategies to create ensemble
  max_ensemble_size: 5            # Maximum strategies in ensemble
  
  combination_methods:
    - "weighted_average"          # Weight by performance
    - "majority_voting"           # Majority vote on signals
    - "confidence_weighted"       # Weight by signal confidence
    - "regime_specific"           # Different strategies per regime
  
  performance_weighting:
    sharpe_weight: 0.4            # Weight for Sharpe ratio
    return_weight: 0.3            # Weight for returns
    consistency_weight: 0.3       # Weight for consistency

# ═══════════════════════════════════════════════════════════════════════════════
# 🔍 LOGGING AND MONITORING
# ═══════════════════════════════════════════════════════════════════════════════
logging:
  level: "INFO"                   # Logging level
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  file_logging:
    enabled: true
    filename: "logs/strategy_evolution.log"
    max_size_mb: 100              # Maximum log file size
    backup_count: 5               # Number of backup files
  
  performance_logging:
    log_all_mutations: false      # Log every mutation (verbose)
    log_performance_changes: true # Log performance changes
    log_regime_changes: true      # Log regime changes

  evolution_tracking:
    detailed_lineage: true        # Track detailed strategy lineage
    mutation_history: true        # Track mutation history
    performance_attribution: true # Track performance attribution

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 HIGH THROUGHPUT EVOLUTION SETTINGS
# ═══════════════════════════════════════════════════════════════════════════════
high_throughput_mode:
  enabled: true                   # Enable high throughput evolution mode

  parallel_processing:
    strategy_evaluation_workers: 200 # EXTREME workers for strategy evaluation (4x increase)
    backtest_workers: 150         # EXTREME workers for backtesting (4x increase)
    mutation_workers: 100         # EXTREME workers for mutations (4x increase)
    crossover_workers: 75         # EXTREME workers for crossover operations (4x increase)

  memory_management:
    strategy_cache_size: 25000    # MASSIVE cache - 25x increase for extreme memory usage
    result_cache_size: 100000     # MASSIVE result cache - 20x increase
    gc_frequency: 10000           # MUCH less frequent GC for performance
    memory_cleanup_threshold: 0.95 # Use 95% of memory before cleanup

  batch_operations:
    mutation_batch_size: 2000     # MASSIVE batch size - 10x increase for extreme throughput
    evaluation_batch_size: 5000   # MASSIVE evaluation batches - 12x increase
    crossover_batch_size: 1000    # MASSIVE crossover batches - 10x increase

  performance_monitoring:
    resource_check_interval: 5    # Check resource usage every 5 seconds for maximum monitoring
    auto_throttle_enabled: false  # DISABLE throttling for maximum resource usage
    performance_logging_interval: 10 # Log performance metrics every 10 seconds
