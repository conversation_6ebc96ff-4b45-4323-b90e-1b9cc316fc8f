#!/usr/bin/env python3
"""
Performance Optimization Utilities for Options Strategy Evolution Agent

This module provides additional optimization utilities and performance comparison tools.
"""

import asyncio
import time
import numpy as np
import polars as pl
import pandas as pd
from concurrent.futures import Process<PERSON>oolExecutor, ThreadPoolExecutor, as_completed
from numba import jit, njit
import logging
from typing import List, Dict, Any, Callable
import functools
import os

logger = logging.getLogger(__name__)

# Performance monitoring decorator
def performance_monitor(func):
    """Decorator to monitor function performance"""
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        try:
            result = await func(*args, **kwargs)
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            logger.info(f"⚡ {func.__name__} executed in {execution_time:.4f}s")
            return result
        except Exception as e:
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            logger.error(f"❌ {func.__name__} failed after {execution_time:.4f}s: {e}")
            raise
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.perf_counter()
        try:
            result = func(*args, **kwargs)
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            logger.info(f"⚡ {func.__name__} executed in {execution_time:.4f}s")
            return result
        except Exception as e:
            end_time = time.perf_counter()
            execution_time = end_time - start_time
            logger.error(f"❌ {func.__name__} failed after {execution_time:.4f}s: {e}")
            raise
    
    return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

# JIT-compiled financial calculations
@njit
def fast_rolling_sharpe(returns: np.ndarray, window: int = 252) -> np.ndarray:
    """Fast rolling Sharpe ratio calculation using JIT"""
    n = len(returns)
    if n < window:
        return np.full(n, np.nan)
    
    sharpe_ratios = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        window_returns = returns[i - window + 1:i + 1]
        mean_return = np.mean(window_returns)
        std_return = np.std(window_returns)
        
        if std_return > 0:
            sharpe_ratios[i] = mean_return / std_return * np.sqrt(252)
        else:
            sharpe_ratios[i] = 0.0
    
    return sharpe_ratios

@njit
def fast_rolling_max_drawdown(prices: np.ndarray, window: int = 252) -> np.ndarray:
    """Fast rolling maximum drawdown calculation using JIT"""
    n = len(prices)
    if n < window:
        return np.full(n, np.nan)
    
    max_drawdowns = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        window_prices = prices[i - window + 1:i + 1]
        running_max = np.maximum.accumulate(window_prices)
        drawdowns = (window_prices - running_max) / running_max
        max_drawdowns[i] = np.min(drawdowns)
    
    return max_drawdowns

@njit
def fast_genetic_crossover(parent1_params: np.ndarray, parent2_params: np.ndarray, 
                          crossover_rate: float = 0.5) -> np.ndarray:
    """Fast genetic algorithm crossover using JIT"""
    n = len(parent1_params)
    child_params = np.empty(n)
    
    for i in range(n):
        if np.random.random() < crossover_rate:
            child_params[i] = parent1_params[i]
        else:
            child_params[i] = parent2_params[i]
    
    return child_params

@njit
def fast_genetic_mutation(params: np.ndarray, mutation_rate: float = 0.1, 
                         mutation_strength: float = 0.1) -> np.ndarray:
    """Fast genetic algorithm mutation using JIT"""
    n = len(params)
    mutated_params = params.copy()
    
    for i in range(n):
        if np.random.random() < mutation_rate:
            mutation_factor = 1.0 + np.random.normal(0, mutation_strength)
            mutated_params[i] *= mutation_factor
    
    return mutated_params

class BatchProcessor:
    """Optimized batch processing for large datasets"""
    
    def __init__(self, max_workers: int = None):
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)
        self.process_pool = ProcessPoolExecutor(max_workers=min(8, os.cpu_count() or 1))
    
    @performance_monitor
    async def process_strategies_parallel(self, strategies: List[Dict], 
                                        processor_func: Callable) -> List[Any]:
        """Process strategies in parallel using thread pool"""
        loop = asyncio.get_event_loop()
        
        # Submit all tasks to thread pool
        futures = [
            loop.run_in_executor(self.thread_pool, processor_func, strategy)
            for strategy in strategies
        ]
        
        # Collect results as they complete
        results = []
        for future in as_completed(futures):
            try:
                result = await future
                results.append(result)
            except Exception as e:
                logger.error(f"Strategy processing failed: {e}")
                results.append(None)
        
        return results
    
    @performance_monitor
    async def batch_file_operations(self, file_operations: List[Callable]) -> List[Any]:
        """Perform file operations in parallel"""
        loop = asyncio.get_event_loop()
        
        futures = [
            loop.run_in_executor(self.thread_pool, operation)
            for operation in file_operations
        ]
        
        results = await asyncio.gather(*futures, return_exceptions=True)
        
        # Filter out exceptions
        successful_results = [r for r in results if not isinstance(r, Exception)]
        failed_count = len(results) - len(successful_results)
        
        if failed_count > 0:
            logger.warning(f"{failed_count} file operations failed")
        
        return successful_results
    
    def cleanup(self):
        """Cleanup thread pools"""
        self.thread_pool.shutdown(wait=True)
        self.process_pool.shutdown(wait=True)

class PolarsOptimizer:
    """Polars-specific optimizations for data processing"""
    
    @staticmethod
    @performance_monitor
    def optimize_performance_analysis(df: pl.DataFrame) -> pl.DataFrame:
        """Optimized performance analysis using Polars lazy evaluation"""
        return (
            df.lazy()
            .with_columns([
                # Vectorized calculations
                pl.col("returns").rolling_mean(window_size=20).alias("ma_20"),
                pl.col("returns").rolling_std(window_size=20).alias("volatility_20"),
                (pl.col("returns").rolling_mean(window_size=20) / 
                 pl.col("returns").rolling_std(window_size=20) * 
                 np.sqrt(252)).alias("rolling_sharpe"),
                
                # Cumulative metrics
                (1 + pl.col("returns")).cumprod().alias("cumulative_returns"),
                pl.col("returns").cumsum().alias("cumulative_pnl"),
                
                # Drawdown calculation
                ((1 + pl.col("returns")).cumprod() / 
                 (1 + pl.col("returns")).cumprod().cummax() - 1).alias("drawdown")
            ])
            .with_columns([
                # Performance metrics
                pl.col("drawdown").min().alias("max_drawdown"),
                (pl.col("returns") > 0).mean().alias("win_rate"),
                pl.col("returns").mean().alias("avg_return"),
                pl.col("returns").std().alias("volatility")
            ])
            .collect()
        )
    
    @staticmethod
    @performance_monitor
    def batch_strategy_comparison(strategies_df: pl.DataFrame) -> pl.DataFrame:
        """Batch compare strategies using Polars"""
        return (
            strategies_df.lazy()
            .group_by("strategy_id")
            .agg([
                pl.col("roi").mean().alias("avg_roi"),
                pl.col("sharpe_ratio").mean().alias("avg_sharpe"),
                pl.col("win_rate").mean().alias("avg_win_rate"),
                pl.col("max_drawdown").mean().alias("avg_drawdown"),
                pl.col("total_trades").sum().alias("total_trades"),
                pl.col("timestamp").max().alias("last_updated")
            ])
            .with_columns([
                # Composite score calculation
                (pl.col("avg_roi") * 0.3 + 
                 pl.col("avg_sharpe") * 0.3 + 
                 pl.col("avg_win_rate") * 0.2 + 
                 (1 - pl.col("avg_drawdown")) * 0.2).alias("composite_score")
            ])
            .sort("composite_score", descending=True)
            .collect()
        )

class VectorBTOptimizer:
    """VectorBT-specific optimizations"""
    
    @staticmethod
    @performance_monitor
    def batch_backtest_strategies(price_data: np.ndarray, 
                                signals_list: List[np.ndarray]) -> List[Dict]:
        """Batch backtest multiple strategies using VectorBT"""
        try:
            import vectorbt as vbt
            
            results = []
            
            # Configure VectorBT for optimal performance
            vbt.settings.array_wrapper['freq'] = 'D'
            vbt.settings.returns['year_freq'] = '252D'
            
            for signals in signals_list:
                try:
                    # Align signals with price data
                    min_len = min(len(signals), len(price_data))
                    aligned_signals = signals[:min_len]
                    aligned_prices = price_data[:min_len]
                    
                    # Create portfolio
                    portfolio = vbt.Portfolio.from_signals(
                        aligned_prices,
                        entries=aligned_signals > 0,
                        exits=aligned_signals < 0,
                        freq='D'
                    )
                    
                    # Calculate comprehensive stats
                    stats = portfolio.stats()
                    
                    result = {
                        'total_return': float(stats.get('Total Return [%]', 0)) / 100,
                        'sharpe_ratio': float(stats.get('Sharpe Ratio', 0)),
                        'max_drawdown': float(stats.get('Max Drawdown [%]', 0)) / 100,
                        'win_rate': float(stats.get('Win Rate [%]', 0)) / 100,
                        'total_trades': int(stats.get('# Trades', 0)),
                        'profit_factor': float(stats.get('Profit Factor', 1)),
                        'expectancy': float(stats.get('Expectancy', 0)),
                        'calmar_ratio': float(stats.get('Calmar Ratio', 0)),
                        'sortino_ratio': float(stats.get('Sortino Ratio', 0))
                    }
                    
                    results.append(result)
                    
                except Exception as e:
                    logger.error(f"VectorBT backtest failed: {e}")
                    results.append(None)
            
            return results
            
        except ImportError:
            logger.error("VectorBT not available")
            return [None] * len(signals_list)

class PerformanceComparator:
    """Compare performance between original and optimized implementations"""
    
    def __init__(self):
        self.results = {}
    
    @performance_monitor
    async def compare_data_processing(self, data_size: int = 100000):
        """Compare Pandas vs Polars performance"""
        # Generate test data
        test_data = {
            'strategy_id': [f'strategy_{i % 100}' for i in range(data_size)],
            'timestamp': pd.date_range('2020-01-01', periods=data_size, freq='H'),
            'roi': np.random.normal(0.05, 0.2, data_size),
            'sharpe_ratio': np.random.normal(1.0, 0.5, data_size),
            'win_rate': np.random.beta(2, 2, data_size),
            'max_drawdown': np.random.exponential(0.1, data_size)
        }
        
        # Pandas processing
        start_time = time.perf_counter()
        df_pandas = pd.DataFrame(test_data)
        pandas_result = (
            df_pandas.groupby('strategy_id')
            .agg({
                'roi': 'mean',
                'sharpe_ratio': 'mean',
                'win_rate': 'mean',
                'max_drawdown': 'mean'
            })
            .reset_index()
        )
        pandas_time = time.perf_counter() - start_time
        
        # Polars processing
        start_time = time.perf_counter()
        df_polars = pl.DataFrame(test_data)
        polars_result = (
            df_polars.group_by('strategy_id')
            .agg([
                pl.col('roi').mean(),
                pl.col('sharpe_ratio').mean(),
                pl.col('win_rate').mean(),
                pl.col('max_drawdown').mean()
            ])
        )
        polars_time = time.perf_counter() - start_time
        
        speedup = pandas_time / polars_time
        
        self.results['data_processing'] = {
            'pandas_time': pandas_time,
            'polars_time': polars_time,
            'speedup': speedup,
            'data_size': data_size
        }
        
        logger.info(f"📊 Data Processing Comparison:")
        logger.info(f"   Pandas: {pandas_time:.4f}s")
        logger.info(f"   Polars: {polars_time:.4f}s")
        logger.info(f"   Speedup: {speedup:.2f}x")
        
        return speedup
    
    @performance_monitor
    async def compare_financial_calculations(self, data_size: int = 10000):
        """Compare standard vs JIT-compiled financial calculations"""
        # Generate test data
        returns = np.random.normal(0.001, 0.02, data_size)
        prices = np.cumprod(1 + returns) * 100
        
        # Standard calculation
        start_time = time.perf_counter()
        standard_sharpe = self._standard_rolling_sharpe(returns, 252)
        standard_time = time.perf_counter() - start_time
        
        # JIT calculation
        start_time = time.perf_counter()
        jit_sharpe = fast_rolling_sharpe(returns, 252)
        jit_time = time.perf_counter() - start_time
        
        speedup = standard_time / jit_time
        
        self.results['financial_calculations'] = {
            'standard_time': standard_time,
            'jit_time': jit_time,
            'speedup': speedup,
            'data_size': data_size
        }
        
        logger.info(f"🧮 Financial Calculations Comparison:")
        logger.info(f"   Standard: {standard_time:.4f}s")
        logger.info(f"   JIT: {jit_time:.4f}s")
        logger.info(f"   Speedup: {speedup:.2f}x")
        
        return speedup
    
    def _standard_rolling_sharpe(self, returns: np.ndarray, window: int) -> np.ndarray:
        """Standard rolling Sharpe calculation for comparison"""
        n = len(returns)
        sharpe_ratios = np.full(n, np.nan)
        
        for i in range(window - 1, n):
            window_returns = returns[i - window + 1:i + 1]
            mean_return = np.mean(window_returns)
            std_return = np.std(window_returns)
            
            if std_return > 0:
                sharpe_ratios[i] = mean_return / std_return * np.sqrt(252)
            else:
                sharpe_ratios[i] = 0.0
        
        return sharpe_ratios
    
    @performance_monitor
    async def compare_parallel_processing(self, num_tasks: int = 1000):
        """Compare sequential vs parallel processing"""
        # Define a CPU-intensive task
        def cpu_intensive_task(x):
            return sum(i**2 for i in range(x))
        
        tasks = [100 + i for i in range(num_tasks)]
        
        # Sequential processing
        start_time = time.perf_counter()
        sequential_results = [cpu_intensive_task(task) for task in tasks]
        sequential_time = time.perf_counter() - start_time
        
        # Parallel processing
        start_time = time.perf_counter()
        with ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:
            parallel_results = list(executor.map(cpu_intensive_task, tasks))
        parallel_time = time.perf_counter() - start_time
        
        speedup = sequential_time / parallel_time
        
        self.results['parallel_processing'] = {
            'sequential_time': sequential_time,
            'parallel_time': parallel_time,
            'speedup': speedup,
            'num_tasks': num_tasks
        }
        
        logger.info(f"⚡ Parallel Processing Comparison:")
        logger.info(f"   Sequential: {sequential_time:.4f}s")
        logger.info(f"   Parallel: {parallel_time:.4f}s")
        logger.info(f"   Speedup: {speedup:.2f}x")
        
        return speedup
    
    def generate_performance_report(self) -> str:
        """Generate comprehensive performance report"""
        report = "\n" + "="*60 + "\n"
        report += "PERFORMANCE OPTIMIZATION REPORT\n"
        report += "="*60 + "\n\n"
        
        total_speedup = 1.0
        
        for test_name, results in self.results.items():
            report += f"{test_name.upper().replace('_', ' ')}:\n"
            report += f"  Data Size/Tasks: {results.get('data_size', results.get('num_tasks', 'N/A'))}\n"
            
            if 'standard_time' in results:
                report += f"  Standard Time: {results['standard_time']:.4f}s\n"
                report += f"  Optimized Time: {results.get('jit_time', results.get('polars_time', 'N/A')):.4f}s\n"
            elif 'sequential_time' in results:
                report += f"  Sequential Time: {results['sequential_time']:.4f}s\n"
                report += f"  Parallel Time: {results['parallel_time']:.4f}s\n"
            elif 'pandas_time' in results:
                report += f"  Pandas Time: {results['pandas_time']:.4f}s\n"
                report += f"  Polars Time: {results['polars_time']:.4f}s\n"
            
            speedup = results['speedup']
            report += f"  Speedup: {speedup:.2f}x\n\n"
            total_speedup *= speedup
        
        report += f"OVERALL ESTIMATED SPEEDUP: {total_speedup:.2f}x\n"
        report += "="*60 + "\n"
        
        return report

# Usage example
async def run_performance_comparison():
    """Run comprehensive performance comparison"""
    comparator = PerformanceComparator()
    
    logger.info("🚀 Starting performance comparison...")
    
    # Run comparisons
    await comparator.compare_data_processing(100000)
    await comparator.compare_financial_calculations(10000)
    await comparator.compare_parallel_processing(1000)
    
    # Generate report
    report = comparator.generate_performance_report()
    print(report)
    
    # Save report
    with open("performance_report.txt", "w") as f:
        f.write(report)
    
    logger.info("📊 Performance comparison completed")

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(run_performance_comparison())