import logging
from typing import Dict, Any
from agents.strategy_generation.data_models import StrategyType

logger = logging.getLogger(__name__)

class ConfigManager:
    """Manages configuration for the Options Strategy Generation Agent."""

    def __init__(self, config_path: str = "config/options_strategy_generation_config.yaml"):
        self.config_path = config_path
        self.config: Dict[str, Any] = {}

    async def load_config(self) -> Dict[str, Any]:
        """Load configuration from file or use default."""
        try:
            # For now, we'll use a default configuration as the original file
            # did not load from a YAML file but had a hardcoded default.
            self.config = {
                'underlying_symbols': ['NIFTY', 'BANKNIFTY'],
                'strategy_types': [
                    # Basic directional buying strategies
                    StrategyType.LONG_CALL,
                    StrategyType.LONG_PUT,
                    StrategyType.PROTECTIVE_PUT, # Often used for hedging a long stock position

                    # ATM buying strategies
                    StrategyType.ATM_LONG_CALL,
                    StrategyType.ATM_LONG_PUT,

                    # OTM buying strategies
                    StrategyType.OTM_LONG_CALL,
                    StrategyType.OTM_LONG_PUT,

                    # Far OTM buying strategies
                    StrategyType.FAR_OTM_LONG_CALL,
                    StrategyType.FAR_OTM_LONG_PUT,
                    
                    # Deep OTM buying strategies
                    StrategyType.DEEP_OTM_LONG_CALL,
                    StrategyType.DEEP_OTM_LONG_PUT,

                    # Intraday buying strategies
                    StrategyType.INTRADAY_SCALPING_CALL,
                    StrategyType.INTRADAY_SCALPING_PUT,
                    StrategyType.INTRADAY_MOMENTUM_CALL,
                    StrategyType.INTRADAY_MOMENTUM_PUT,
                    StrategyType.INTRADAY_REVERSAL_CALL,
                    StrategyType.INTRADAY_REVERSAL_PUT,
                    
                    # Gamma Scalping (Long)
                    StrategyType.GAMMA_SCALPING_LONG,
                    StrategyType.DELTA_NEUTRAL_GAMMA_SCALP,
                    
                    # Volatility Breakout (Long)
                    StrategyType.VOLATILITY_BREAKOUT_LONG,
                    StrategyType.VIX_BASED_STRATEGY,

                    # Volatility buying strategies
                    StrategyType.LONG_STRADDLE,
                    StrategyType.LONG_STRANGLE,
                    StrategyType.REVERSE_IRON_CONDOR, # Net debit strategy
                    StrategyType.REVERSE_IRON_BUTTERFLY, # Net debit strategy

                    # Spread buying strategies (net debit spreads)
                    StrategyType.BULL_CALL_SPREAD, # Debit spread
                    StrategyType.BEAR_PUT_SPREAD,  # Debit spread
                    StrategyType.CALL_CALENDAR_SPREAD, # Debit spread
                    StrategyType.PUT_CALENDAR_SPREAD,  # Debit spread
                    StrategyType.DIAGONAL_CALL_SPREAD, # Net debit spread
                    StrategyType.DIAGONAL_PUT_SPREAD,  # Net debit spread

                    # Ratio buying strategies (backspreads are net debit)
                    StrategyType.RATIO_CALL_BACKSPREAD,
                    StrategyType.RATIO_PUT_BACKSPREAD,

                    # Collar strategies (often net zero or small debit/credit)
                    StrategyType.COLLAR,

                    # Synthetic buying strategies
                    StrategyType.SYNTHETIC_LONG,
                    StrategyType.SYNTHETIC_CALL, # Synthetic long call
                    StrategyType.SYNTHETIC_PUT,  # Synthetic long put

                    # Complex Multi-leg buying strategies (net debit)
                    StrategyType.BUTTERFLY_SPREAD, # Net debit strategy
                    StrategyType.CONDOR_SPREAD, # Net debit strategy
                    StrategyType.CHRISTMAS_TREE, # Net debit strategy

                    # Indian market specific buying strategies
                    StrategyType.WEEKLY_EXPIRY_STRADDLE,
                    StrategyType.MONTHLY_EXPIRY_STRANGLE,
                    StrategyType.BANKNIFTY_BUTTERFLY # Assuming this is a buying butterfly
                ],
                'min_probability_of_profit': 0.35,  # Lowered for more strategies
                'max_risk_per_trade': 0.02,  # 2% of capital
                'min_risk_reward_ratio': 1.2,  # Lowered for more strategies
                'max_strategies_per_underlying': 100,  # Increased for more strategies
                'optimization_iterations': 100,
                'atm_threshold': 0.02,  # 2% from spot for ATM
                'otm_threshold': 0.05,  # 5% from spot for OTM
                'far_otm_threshold': 0.10  # 10% from spot for Far OTM
            }
            logger.info("[CONFIG] Configuration loaded successfully")
            return self.config
        except Exception as e:
            logger.error(f"[ERROR] Failed to load configuration: {e}")
            raise
