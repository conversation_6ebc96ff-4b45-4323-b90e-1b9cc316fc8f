import logging
from typing import List, Dict
from agents.strategy_generation.data_models import OptionsStrategy

logger = logging.getLogger(__name__)

class StrategyOptimizer:
    """Filters and optimizes generated options strategies."""

    def __init__(self, config: Dict):
        self.config = config

    async def filter_strategies(self, strategies: List[OptionsStrategy]) -> List[OptionsStrategy]:
        """Filter strategies based on criteria."""
        try:
            filtered = []
            
            for strategy in strategies:
                # Apply filters
                if (strategy.probability_of_profit >= self.config['min_probability_of_profit'] and
                    strategy.risk_reward_ratio >= self.config['min_risk_reward_ratio'] and
                    strategy.max_loss <= 10000):  # Max loss limit
                    
                    filtered.append(strategy)
            
            logger.info(f"[FILTER] Filtered {len(filtered)} strategies from {len(strategies)}")
            return filtered
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to filter strategies: {e}")
            return strategies
    
    async def optimize_strategies(self, strategies: List[OptionsStrategy]) -> List[OptionsStrategy]:
        """Optimize strategy parameters."""
        try:
            # Sort by risk-reward ratio and probability of profit
            optimized = sorted(
                strategies,
                key=lambda s: (s.risk_reward_ratio * s.probability_of_profit),
                reverse=True
            )
            
            # Limit number of strategies per underlying
            max_strategies = self.config['max_strategies_per_underlying']
            optimized = optimized[:max_strategies]
            
            logger.info(f"[OPTIMIZE] Optimized to {len(optimized)} strategies")
            return optimized
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to optimize strategies: {e}")
            return strategies
