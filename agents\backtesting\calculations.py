"""
High-performance calculation functions for the backtesting engine.
"""
import logging
import multiprocessing as mp
import numpy as np
import polars as pl
from typing import Dict, Tuple

logger = logging.getLogger(__name__)

try:
    import numexpr as ne
    NUMEXPR_AVAILABLE = True
    ne.set_num_threads(mp.cpu_count())
    ne.set_vml_accuracy_mode('high')
    ne.set_vml_num_threads(mp.cpu_count())
    logging.info(f"NumExpr loaded - {mp.cpu_count()} threads configured")
except ImportError:
    NUMEXPR_AVAILABLE = False
    logging.warning("numexpr not installed. Install with: pip install numexpr")

try:
    from numba import jit, njit, prange
    import numba as nb
    NUMBA_AVAILABLE = True
    nb.config.THREADING_LAYER = 'threadsafe'
    nb.config.NUMBA_NUM_THREADS = mp.cpu_count()
    logging.info(f"Numba JIT loaded - {mp.cpu_count()} threads configured")
except ImportError:
    NUMBA_AVAILABLE = False
    logging.warning("numba not installed. Install with: pip install numba")
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def njit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    prange = range

if NUMBA_AVAILABLE:
    @njit(parallel=True, cache=True, fastmath=True)
    def calculate_returns_numba(prices: np.ndarray) -> np.ndarray:
        """Calculate returns using Numba JIT compilation with parallel processing"""
        n = len(prices)
        if n < 2:
            return np.empty(0, dtype=np.float64)
        returns = np.empty(n - 1, dtype=np.float64)
        for i in prange(1, n):
            if prices[i-1] != 0:
                returns[i-1] = (prices[i] - prices[i-1]) / prices[i-1]
            else:
                returns[i-1] = 0.0
        return returns

    @njit(parallel=True, cache=True, fastmath=True)
    def calculate_rolling_mean_numba(data: np.ndarray, window: int) -> np.ndarray:
        """Calculate rolling mean using optimized Numba JIT compilation"""
        n = len(data)
        result = np.empty(n, dtype=np.float64)
        result[:window-1] = np.nan

        if n >= window:
            cumsum = np.cumsum(data)
            result[window-1] = cumsum[window-1] / window
            for i in prange(window, n):
                result[i] = (cumsum[i] - cumsum[i-window]) / window
        return result

    @njit(parallel=True, cache=True, fastmath=True)
    def calculate_rolling_std_numba(data: np.ndarray, window: int) -> np.ndarray:
        """Calculate rolling standard deviation using optimized Numba JIT"""
        n = len(data)
        result = np.empty(n, dtype=np.float64)
        result[:window-1] = np.nan

        for i in prange(window-1, n):
            window_data = data[i-window+1:i+1]
            result[i] = np.std(window_data)
        return result

    @njit(parallel=True, cache=True, fastmath=True)
    def calculate_max_drawdown_numba(equity_curve: np.ndarray) -> float:
        """Calculate maximum drawdown using optimized Numba JIT compilation"""
        n = len(equity_curve)
        if n == 0:
            return 0.0

        peak = equity_curve[0]
        max_dd = 0.0

        for i in range(1, n):
            if equity_curve[i] > peak:
                peak = equity_curve[i]

            if peak > 0:
                drawdown = (peak - equity_curve[i]) / peak
                if drawdown > max_dd:
                    max_dd = drawdown
        return max_dd

    @njit(parallel=True, cache=True, fastmath=True)
    def norm_cdf_approx(x: float) -> float:
        """Fast normal CDF approximation for Black-Scholes"""
        a1 = 0.254829592
        a2 = -0.284496736
        a3 = 1.421413741
        a4 = -1.453152027
        a5 = 1.061405429
        p = 0.3275911

        sign = 1.0 if x >= 0 else -1.0
        x_abs = abs(x) / np.sqrt(2.0)

        t = 1.0 / (1.0 + p * x_abs)
        y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * np.exp(-x_abs * x_abs)

        return 0.5 * (1.0 + sign * y)

    @njit(parallel=True, cache=True, fastmath=True)
    def vectorized_black_scholes_numba(S: np.ndarray, K: np.ndarray, T: np.ndarray,
                                      r: float, sigma: np.ndarray, option_type: np.ndarray) -> np.ndarray:
        """Ultra-fast vectorized Black-Scholes calculation using Numba"""
        n = len(S)
        prices = np.empty(n, dtype=np.float64)

        for i in prange(n):
            if T[i] <= 1e-8:
                prices[i] = max(S[i] - K[i] if option_type[i] == 1 else K[i] - S[i], 0.0)
            else:
                sqrt_T = np.sqrt(T[i])
                d1 = (np.log(S[i] / K[i]) + (r + 0.5 * sigma[i]**2) * T[i]) / (sigma[i] * sqrt_T)
                d2 = d1 - sigma[i] * sqrt_T

                if option_type[i] == 1:  # Call
                    prices[i] = S[i] * norm_cdf_approx(d1) - K[i] * np.exp(-r * T[i]) * norm_cdf_approx(d2)
                else:  # Put
                    prices[i] = K[i] * np.exp(-r * T[i]) * norm_cdf_approx(-d2) - S[i] * norm_cdf_approx(-d1)
                prices[i] = max(prices[i], 0.0)
        return prices

    @njit(parallel=True, cache=True, fastmath=True)
    def calculate_greeks_numba(S: np.ndarray, K: np.ndarray, T: np.ndarray,
                              r: float, sigma: np.ndarray, option_type: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Calculate Greeks (Delta, Gamma, Theta, Vega) using Numba"""
        n = len(S)
        delta = np.empty(n, dtype=np.float64)
        gamma = np.empty(n, dtype=np.float64)
        theta = np.empty(n, dtype=np.float64)
        vega = np.empty(n, dtype=np.float64)

        for i in prange(n):
            if T[i] <= 1e-8:
                delta[i] = gamma[i] = theta[i] = vega[i] = 0.0
            else:
                sqrt_T = np.sqrt(T[i])
                d1 = (np.log(S[i] / K[i]) + (r + 0.5 * sigma[i]**2) * T[i]) / (sigma[i] * sqrt_T)
                d2 = d1 - sigma[i] * sqrt_T
                nd1 = np.exp(-0.5 * d1**2) / np.sqrt(2 * np.pi)

                if option_type[i] == 1:  # Call
                    delta[i] = norm_cdf_approx(d1)
                    theta[i] = (-S[i] * nd1 * sigma[i] / (2 * sqrt_T) - r * K[i] * np.exp(-r * T[i]) * norm_cdf_approx(d2))
                else:  # Put
                    delta[i] = norm_cdf_approx(d1) - 1.0
                    theta[i] = (-S[i] * nd1 * sigma[i] / (2 * sqrt_T) + r * K[i] * np.exp(-r * T[i]) * norm_cdf_approx(-d2))

                gamma[i] = nd1 / (S[i] * sigma[i] * sqrt_T)
                vega[i] = S[i] * nd1 * sqrt_T / 100.0
        return delta, gamma, theta, vega

def optimize_with_numexpr(expression: str, local_dict: Dict[str, np.ndarray]) -> np.ndarray:
    """Use numexpr for ultra-fast numerical expressions"""
    if NUMEXPR_AVAILABLE:
        try:
            return ne.evaluate(expression, local_dict=local_dict)
        except Exception as e:
            logger.warning(f"Numexpr evaluation failed: {e}, falling back to numpy")
            return eval(expression, {"np": np}, local_dict)
    else:
        return eval(expression, {"np": np}, local_dict)
