import logging
import polars as pl
import json
import aiofiles
from datetime import datetime
from pathlib import Path
from typing import List
from agents.strategy_generation.data_models import OptionsStrategy

logger = logging.getLogger(__name__)

class StrategySaver:
    """Saves generated options strategies to files."""

    def __init__(self, strategies_path: Path):
        self.strategies_path = strategies_path
        self.strategies_path.mkdir(parents=True, exist_ok=True)

    async def save_comprehensive_strategies(self, strategies: List[OptionsStrategy], underlying: str, timeframe: str):
        """Save comprehensive strategies to file."""
        try:
            if not strategies:
                logger.warning(f"[SAVE] No strategies to save for {underlying} {timeframe}")
                return

            # Create strategy data for saving with proper type conversion
            strategy_data = []
            for strategy in strategies:
                # Convert break_even_points to string
                break_even_str = ','.join([str(float(bp)) for bp in strategy.break_even_points]) if strategy.break_even_points else ''

                strategy_dict = {
                    'strategy_id': str(strategy.strategy_id),
                    'strategy_type': str(strategy.strategy_type.value),
                    'underlying': str(strategy.underlying),
                    'max_profit': float(strategy.max_profit) if strategy.max_profit != float('inf') else 999999.0,
                    'max_loss': float(strategy.max_loss),
                    'break_even_points': break_even_str,
                    'probability_of_profit': float(strategy.probability_of_profit),
                    'risk_reward_ratio': float(strategy.risk_reward_ratio),
                    'net_premium': float(strategy.net_premium),
                    'margin_required': float(strategy.margin_required),
                    'target_profit': float(strategy.target_profit),
                    'stop_loss': float(strategy.stop_loss),
                    'created_at': str(strategy.created_at.isoformat()),
                    'timeframe': str(timeframe),
                    'num_legs': len(strategy.legs)
                }
                strategy_data.append(strategy_dict)

            # Save to JSON file to avoid parquet Object datatype issues
            if strategy_data:
                filename = f"strategies_{underlying}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                filepath = self.strategies_path / filename

                async with aiofiles.open(filepath, 'w') as f:
                    await f.write(json.dumps(strategy_data, indent=2))

                logger.info(f"[SAVE] Saved {len(strategies)} strategies for {underlying} {timeframe} to {filename}")
            else:
                logger.warning(f"[SAVE] No valid strategy data to save for {underlying} {timeframe}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to save strategies: {e}")

    async def save_strategies(self, generated_strategies: Dict[str, List[OptionsStrategy]]):
        """Save generated strategies."""
        try:
            logger.info("[SAVE] Saving generated strategies...")
            
            all_strategies = []
            
            for underlying, strategies in generated_strategies.items():
                for strategy in strategies:
                    all_strategies.append(strategy.to_dict())
            
            if all_strategies:
                # Convert to DataFrame and save
                strategies_df = pl.DataFrame(all_strategies)
                
                filename = f"generated_strategies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.parquet"
                filepath = self.strategies_path / filename
                
                strategies_df.write_parquet(filepath)
                
                # Also save as JSON for readability
                json_filename = f"generated_strategies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                json_filepath = self.strategies_path / json_filename
                
                async with aiofiles.open(json_filepath, 'w') as f:
                    await f.write(json.dumps(all_strategies, indent=2, default=str))
                
                logger.info(f"[SUCCESS] Saved {len(all_strategies)} strategies")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to save strategies: {e}")
