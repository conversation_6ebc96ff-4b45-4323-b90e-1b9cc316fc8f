#!/usr/bin/env python3
"""
Options Strategy Evolution Agent - Modular Version

This is the main orchestrator for the modular options strategy evolution system.
It uses the new modular architecture with focused components for better maintainability.

Key Features:
- Modular architecture with focused components
- Efficient data usage from existing features data
- Direct backtesting agent integration
- Performance-based genetic algorithm operations
- Real-time strategy adaptation
- Population management and diversity maintenance

The agent maintains a population of strategies and continuously evolves them
to find optimal trading approaches for different market conditions.
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import aiofiles

# Import modular evolution components
from agents.evolution import (
    DataIntegrationManager,
    PerformanceAnalyzer,
    GeneticOperations,
    StrategyManager,
    EvolutionCore
)
from agents.evolution.strategy_management import StrategyConfig, StrategyStatus

logger = logging.getLogger(__name__)

class OptionsStrategyEvolutionAgent:
    """Main orchestrator for options strategy evolution using modular architecture"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._get_default_config()
        self.is_initialized = False
        
        # Initialize evolution core
        self.evolution_core = EvolutionCore(self.config)
        
        # Quick access to modules
        self.data_manager = self.evolution_core.data_manager
        self.performance_analyzer = self.evolution_core.performance_analyzer
        self.genetic_ops = self.evolution_core.genetic_ops
        self.strategy_manager = self.evolution_core.strategy_manager
        
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'population_size': 100,
            'mutation_rate': 0.15,
            'crossover_rate': 0.8,
            'selection_pressure': 0.3,
            'evolution_interval': 3600,  # 1 hour
            'performance_check_interval': 300,  # 5 minutes
            'max_workers': 8,
            'timeframes': ['1min', '3min', '5min', '15min'],
            'symbols': ['NIFTY', 'BANKNIFTY'],
            'backtest_days': 30,
            'auto_start_evolution': True,
            'export_strategies': True,
            'export_path': 'config/options_strategies.yaml',
            'buy_only_mode': True # New configuration parameter
        }

    def _is_buy_only_strategy(self, strategy_config: Dict[str, Any]) -> bool:
        """
        Checks if a strategy is compatible with options buying only.
        A strategy is considered buy-only if its 'action' in entry_conditions is 'BUY_CE' or 'BUY_PE',
        or if its name/description explicitly indicates a long/buy position and not a short/sell position.
        """
        strategy_id = strategy_config.get('strategy_id', '').lower()
        name = strategy_config.get('name', '').lower()
        description = strategy_config.get('description', '').lower()
        
        # Check entry conditions for explicit BUY actions
        entry_conditions = strategy_config.get('entry_conditions', [])
        for condition in entry_conditions:
            if isinstance(condition, dict) and condition.get('action') in ['BUY_CE', 'BUY_PE']:
                return True
            # Also check for 'action' within parameters if structured differently
            if isinstance(condition, str) and ('buy_ce' in condition or 'buy_pe' in condition):
                return True

        # Check strategy name/description for keywords
        if ('long' in name or 'buy' in name or 'call' in name or 'put' in name) and \
           ('short' not in name and 'sell' not in name and 'bear' not in name and 'put_spread' not in name and 'call_spread' not in name):
            return True
        
        if ('long' in description or 'buy' in description or 'call' in description or 'put' in description) and \
           ('short' not in description and 'sell' not in description and 'bear' not in description and 'put_spread' not in description and 'call_spread' not in description):
            return True

        # Default to false if no clear buy indication or if it's a spread/short strategy
        return False
    
    async def initialize(self, **kwargs) -> bool:
        """Initialize the evolution agent"""
        try:
            logger.info("[EVOLUTION] Initializing Options Strategy Evolution Agent...")

            # Store kwargs for potential use by evolution core
            self.init_kwargs = kwargs

            # Initialize evolution core
            if not await self.evolution_core.initialize():
                raise RuntimeError("Failed to initialize evolution core")

            # Load or create initial strategies if registry is empty
            if len(self.strategy_manager.strategy_registry) == 0:
                await self._load_initial_strategies()

            self.is_initialized = True
            logger.info("[EVOLUTION] Options Strategy Evolution Agent initialized successfully")

            # Note: Evolution will be started in the start() method to avoid double-starting

            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Evolution agent initialization failed: {e}")
            return False

    async def start(self, **kwargs) -> bool:
        """Start the evolution agent"""
        try:
            if not self.is_initialized:
                logger.error("[ERROR] Agent not initialized, cannot start")
                return False

            logger.info("[START] Starting Options Strategy Evolution Agent...")

            # Start evolution process for continuous operation
            if self.config.get('auto_start_evolution', True):
                logger.info("[EVOLUTION] Starting evolution loop...")
                # Run evolution in the foreground to keep the agent alive
                await self.start_evolution()

            logger.info("[SUCCESS] Options Strategy Evolution Agent started successfully")
            return True

        except Exception as e:
            logger.error(f"[ERROR] Failed to start evolution agent: {e}")
            return False

    async def start_evolution(self):
        """Start the evolution process"""
        try:
            if not self.is_initialized:
                logger.error("[ERROR] Agent not initialized, cannot start evolution")
                return
            
            logger.info("[EVOLUTION] Starting strategy evolution process...")
            await self.evolution_core.start_evolution_loop()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to start evolution: {e}")
    
    async def stop_evolution(self):
        """Stop the evolution process"""
        try:
            logger.info("[EVOLUTION] Stopping strategy evolution process...")
            await self.evolution_core.stop_evolution_loop()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to stop evolution: {e}")
    
    async def trigger_evolution_cycle(self):
        """Manually trigger an evolution cycle"""
        try:
            if not self.is_initialized:
                logger.error("[ERROR] Agent not initialized, cannot trigger evolution")
                return
            
            await self.evolution_core.trigger_evolution_cycle()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to trigger evolution cycle: {e}")
    
    async def add_strategy(self, strategy_config: Dict[str, Any]) -> bool:
        """Add a new strategy to the evolution population"""
        try:
            # Convert dict to StrategyConfig if needed
            if isinstance(strategy_config, dict):
                strategy = StrategyConfig.from_dict(strategy_config)
            else:
                strategy = strategy_config
            
            buy_only_mode = self.config.get('buy_only_mode', False)
            if buy_only_mode and not self._is_buy_only_strategy(strategy.to_dict()):
                logger.warning(f"[EVOLUTION] Cannot add non-buy-only strategy in buy-only mode: {strategy.strategy_id}")
                return False

            return await self.strategy_manager.add_strategy(strategy)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to add strategy: {e}")
            return False
    
    async def remove_strategy(self, strategy_id: str) -> bool:
        """Remove a strategy from the evolution population"""
        try:
            return await self.strategy_manager.remove_strategy(strategy_id)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to remove strategy: {e}")
            return False
    
    async def get_strategy_performance(self, strategy_id: str) -> Optional[Dict[str, Any]]:
        """Get performance metrics for a specific strategy"""
        try:
            if strategy_id in self.performance_analyzer.performance_cache:
                metrics = self.performance_analyzer.performance_cache[strategy_id]
                return self.performance_analyzer.get_performance_summary(metrics)
            else:
                logger.warning(f"[WARNING] No performance data found for strategy {strategy_id}")
                return None
                
        except Exception as e:
            logger.error(f"[ERROR] Failed to get strategy performance: {e}")
            return None
    
    async def get_top_strategies(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top performing strategies"""
        try:
            all_strategies = list(self.strategy_manager.strategy_registry.values())
            strategy_dicts = [strategy.to_dict() for strategy in all_strategies]
            
            # Get top performers
            top_performers = await self.genetic_ops.select_top_performers(
                strategy_dicts, min(1.0, limit / len(strategy_dicts))
            )
            
            # Add performance data
            top_strategies_with_performance = []
            for strategy_dict in top_performers[:limit]:
                strategy_id = strategy_dict['strategy_id']
                performance = await self.get_strategy_performance(strategy_id)
                
                strategy_info = {
                    'strategy': strategy_dict,
                    'performance': performance
                }
                top_strategies_with_performance.append(strategy_info)
            
            return top_strategies_with_performance
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get top strategies: {e}")
            return []
    
    async def get_evolution_status(self) -> Dict[str, Any]:
        """Get current evolution status"""
        try:
            return await self.evolution_core.get_evolution_status()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get evolution status: {e}")
            return {'error': str(e)}
    
    async def export_strategies(self, output_path: str = None) -> bool:
        """Export evolved strategies to YAML file"""
        try:
            export_path = output_path or self.config.get('export_path', 'config/options_strategies.yaml')
            return await self.strategy_manager.export_strategies_to_yaml(export_path)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to export strategies: {e}")
            return False
    
    async def get_available_timeframes(self) -> List[str]:
        """Get available timeframes from features data"""
        try:
            return await self.data_manager.get_available_timeframes()
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get available timeframes: {e}")
            return []
    
    async def get_data_summary(self, timeframe: str = "5min") -> Dict[str, Any]:
        """Get summary of available data"""
        try:
            return await self.data_manager.get_data_summary(timeframe)
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to get data summary: {e}")
            return {}
    
    async def _load_initial_strategies(self):
        """Load initial strategies from the data/strategies directory."""
        try:
            logger.info("[EVOLUTION] Loading initial strategy population...")
            strategies_path = Path("data/strategies")
            strategy_files = list(strategies_path.glob("generated_strategies_*.json"))

            if not strategy_files:
                logger.warning("No strategy files found, creating a default set.")
                # Fallback to creating a few default strategies if none are found
                # In a real scenario, you might want to handle this differently
                return

            latest_file = max(strategy_files, key=lambda x: x.stat().st_mtime)
            async with aiofiles.open(latest_file, 'r') as f:
                content = await f.read()
                initial_strategies = json.loads(content)

            added_count = 0
            buy_only_mode = self.config.get('buy_only_mode', False)

            for strategy_dict in initial_strategies:
                # Ensure the dictionary conforms to StrategyConfig structure
                strategy_config = {
                    'strategy_id': strategy_dict.get('strategy_name', f"strategy_{added_count}"),
                    'name': strategy_dict.get('strategy_name', 'Unnamed Strategy'),
                    'description': strategy_dict.get('description', ''),
                    'parameters': strategy_dict.get('parameters', {}),
                    'entry_conditions': strategy_dict.get('entry_conditions', []),
                    'exit_conditions': strategy_dict.get('exit_conditions', []),
                    'risk_management': strategy_dict.get('risk_management', {}),
                    'market_outlook': strategy_dict.get('market_outlook', 'neutral'),
                    'volatility_outlook': strategy_dict.get('volatility_outlook', 'normal'),
                    'timeframe': strategy_dict.get('timeframe', '5min'),
                    'status': strategy_dict.get('status', 'experimental'),
                    'tags': strategy_dict.get('tags', ['loaded'])
                }
                
                # Filter strategies if buy_only_mode is enabled
                if buy_only_mode and not self._is_buy_only_strategy(strategy_config):
                    logger.info(f"[EVOLUTION] Skipping non-buy-only strategy: {strategy_config['strategy_id']}")
                    continue

                strategy = StrategyConfig.from_dict(strategy_config)
                if await self.strategy_manager.add_strategy(strategy):
                    added_count += 1

            logger.info(f"[EVOLUTION] Loaded {added_count} initial strategies from {latest_file.name}")

        except Exception as e:
            logger.error(f"[ERROR] Failed to load initial strategies: {e}")
    
    async def cleanup(self):
        """Cleanup resources"""
        try:
            await self.evolution_core.cleanup()
            logger.info("[EVOLUTION] Evolution agent cleanup completed")
            
        except Exception as e:
            logger.error(f"[ERROR] Evolution agent cleanup failed: {e}")

# For backward compatibility, keep some key functions from the original agent
# These will be imported by other modules that depend on them

def calculate_sharpe_ratio_fast(returns):
    """Fast Sharpe ratio calculation"""
    import numpy as np
    if len(returns) <= 1:
        return 0.0
    mean_return = np.mean(returns)
    std_return = np.std(returns)
    return mean_return / max(std_return, 0.001)

def calculate_max_drawdown_fast(returns):
    """Fast maximum drawdown calculation"""
    import numpy as np
    cumulative = np.cumsum(returns)
    running_max = np.maximum.accumulate(cumulative)
    drawdown = cumulative - running_max
    return np.min(drawdown)

def calculate_win_rate_fast(returns):
    """Fast win rate calculation"""
    import numpy as np
    return np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0.0

def calculate_profit_factor_fast(returns):
    """Fast profit factor calculation"""
    import numpy as np
    gross_profit = np.sum(returns[returns > 0])
    gross_loss = np.abs(np.sum(returns[returns < 0]))
    return gross_profit / max(gross_loss, 0.001)

def calculate_sortino_ratio_fast(returns):
    """Fast Sortino ratio calculation"""
    import numpy as np
    if len(returns) <= 1:
        return 0.0
    mean_return = np.mean(returns)
    downside_returns = returns[returns < 0]
    downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0.001
    return mean_return / downside_std

def calculate_expectancy_fast(returns):
    """Fast expectancy calculation"""
    import numpy as np
    win_rate = calculate_win_rate_fast(returns)
    avg_win = np.mean(returns[returns > 0]) if np.any(returns > 0) else 0
    avg_loss = np.mean(returns[returns < 0]) if np.any(returns < 0) else 0
    return (win_rate * avg_win) + ((1 - win_rate) * avg_loss)
