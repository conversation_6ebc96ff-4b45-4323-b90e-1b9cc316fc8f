"""
Data loading utilities for the backtesting engine.
"""
import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

import aiofiles
import polars as pl
import yaml
from datetime import timezone

logger = logging.getLogger(__name__)

DATA_PATH = Path("data")
STRATEGIES_PATH = DATA_PATH / "strategies"
HISTORICAL_PATH = DATA_PATH / "historical"
FEATURES_PATH = DATA_PATH / "features"


async def load_strategies(strategies_file: Optional[str] = None) -> List[Dict]:
    """Load strategies from YAML or the latest generated file."""
    if strategies_file:
        return await _load_strategies_from_yaml(strategies_file)
    return await _load_latest_generated_strategies()


async def _load_strategies_from_yaml(file_path: str) -> List[Dict]:
    """Load strategies from a YAML file."""
    try:
        logger.info(f"Loading strategies from YAML file: {file_path}")
        async with aiofiles.open(file_path, 'r') as f:
            content = await f.read()
            config = yaml.safe_load(content)
        
        strategies = config.get('strategies', [])
        logger.info(f"Loaded {len(strategies)} strategies from YAML")
        return strategies
    except Exception as e:
        logger.error(f"Failed to load strategies from {file_path}: {e}")
        return []


async def _load_latest_generated_strategies() -> List[Dict]:
    """Load the latest generated strategies file and enhance with trading conditions."""
    try:
        logger.info("Loading latest generated strategies...")
        strategy_files = list(STRATEGIES_PATH.glob("generated_strategies_*.json"))
        if not strategy_files:
            logger.warning("No strategy files found")
            return []

        latest_file = max(strategy_files, key=lambda x: x.stat().st_mtime)
        async with aiofiles.open(latest_file, 'r') as f:
            content = await f.read()
            strategies = json.loads(content)

        # Enhance strategies with trading conditions
        enhanced_strategies = []
        for strategy in strategies:
            enhanced_strategy = _add_trading_conditions(strategy)
            enhanced_strategies.append(enhanced_strategy)

        logger.info(f"Loaded and enhanced {len(enhanced_strategies)} strategies from {latest_file.name}")
        return enhanced_strategies
    except Exception as e:
        logger.error(f"Failed to load latest generated strategies: {e}")
        return []


async def load_feature_engineered_data(start_date: str, end_date: str) -> Optional[pl.DataFrame]:
    """Load and combine feature-engineered options data for a given date range."""
    try:
        logger.info("Loading feature-engineered data...")
        all_data = []
        timeframes = ['1min', '3min', '5min', '15min']

        for timeframe in timeframes:
            timeframe_path = FEATURES_PATH / timeframe
            if not timeframe_path.exists():
                logger.warning(f"No feature data found for timeframe: {timeframe}")
                continue

            files = list(timeframe_path.glob("*.parquet"))
            for file in files:
                try:
                    df = pl.read_parquet(file)
                    all_data.append(df)
                except Exception as e:
                    logger.warning(f"Failed to load {file}: {e}")

        if not all_data:
            logger.warning("No feature-engineered data files found")
            return None

        # Harmonize data types before concatenation to avoid type conflicts
        harmonized_data = []
        for df in all_data:
            # Ensure consistent data types for common columns
            if 'strike_price' in df.columns:
                df = df.with_columns(pl.col('strike_price').cast(pl.Float64, strict=False))
            if 'underlying' in df.columns:
                df = df.with_columns(pl.col('underlying').cast(pl.Utf8, strict=False))
            if 'option_type' in df.columns:
                df = df.with_columns(pl.col('option_type').cast(pl.Utf8, strict=False))
            harmonized_data.append(df)

        combined_data = pl.concat(harmonized_data, how="diagonal")
        
        start_dt = datetime.strptime(start_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)
        end_dt = datetime.strptime(end_date, "%Y-%m-%d").replace(tzinfo=timezone.utc)

        # Ensure timestamp column exists and is of datetime type
        if 'timestamp' not in combined_data.columns:
            if 'date' in combined_data.columns and 'time' in combined_data.columns:
                 combined_data = combined_data.with_columns(
                    (pl.col('date').cast(pl.Utf8) + " " + pl.col('time').cast(pl.Utf8))
                    .str.to_datetime()
                    .alias('timestamp')
                )
            else:
                logger.error("No suitable timestamp column found in feature data")
                return None
        
        if combined_data['timestamp'].dtype != pl.Datetime:
             combined_data = combined_data.with_columns(
                pl.col('timestamp').cast(pl.Datetime)
            )

        date_filtered = combined_data.filter(
            (pl.col("timestamp") >= start_dt) & (pl.col("timestamp") <= end_dt)
        )
        
        logger.info(f"Loaded {date_filtered.height} feature-engineered records")
        return date_filtered
    except Exception as e:
        logger.error(f"Failed to load feature-engineered data: {e}")
        return None


def _add_trading_conditions(strategy: Dict) -> Dict:
    """Add realistic trading conditions to a strategy based on its type."""
    strategy_type = strategy.get('strategy_type', 'long_call')
    underlying = strategy.get('underlying', 'NIFTY')

    # Create a copy to avoid modifying the original
    enhanced_strategy = strategy.copy()

    # Add entry and exit conditions based on strategy type
    if strategy_type in ['long_call', 'LC']:
        enhanced_strategy['entry_conditions'] = [
            'bullish_trend',
            'positive_momentum',
            'volatility_increase',
            'volume_confirmation'
        ]
        enhanced_strategy['exit_conditions'] = [
            'trend_break_down',
            'negative_momentum',
            'volatility_crush'
        ]
        enhanced_strategy['max_holding_period'] = 5  # 5 periods max
        enhanced_strategy['profit_target'] = 0.5  # 50% profit target
        enhanced_strategy['stop_loss'] = 0.3  # 30% stop loss

    elif strategy_type in ['long_put', 'LP']:
        enhanced_strategy['entry_conditions'] = [
            'bearish_trend',
            'negative_momentum',
            'volatility_increase',
            'volume_confirmation'
        ]
        enhanced_strategy['exit_conditions'] = [
            'trend_break_up',
            'positive_momentum',
            'volatility_crush'
        ]
        enhanced_strategy['max_holding_period'] = 5
        enhanced_strategy['profit_target'] = 0.5
        enhanced_strategy['stop_loss'] = 0.3

    elif strategy_type in ['short_call', 'SC']:
        enhanced_strategy['entry_conditions'] = [
            'resistance_level',
            'high_volatility',
            'low_momentum'
        ]
        enhanced_strategy['exit_conditions'] = [
            'strong_upward_move',
            'volatility_crush'
        ]
        enhanced_strategy['max_holding_period'] = 10  # Longer holding for short options
        enhanced_strategy['profit_target'] = 0.3  # 30% profit target
        enhanced_strategy['stop_loss'] = 1.0  # 100% stop loss (limited profit, unlimited loss)

    elif strategy_type in ['short_put', 'SP']:
        enhanced_strategy['entry_conditions'] = [
            'support_level',
            'high_volatility',
            'low_momentum'
        ]
        enhanced_strategy['exit_conditions'] = [
            'strong_downward_move',
            'volatility_crush'
        ]
        enhanced_strategy['max_holding_period'] = 10
        enhanced_strategy['profit_target'] = 0.3
        enhanced_strategy['stop_loss'] = 1.0

    else:
        # Default conditions for unknown strategy types
        enhanced_strategy['entry_conditions'] = [
            'conservative_bullish'
        ]
        enhanced_strategy['exit_conditions'] = [
            'adverse_move'
        ]
        enhanced_strategy['max_holding_period'] = 3
        enhanced_strategy['profit_target'] = 0.2
        enhanced_strategy['stop_loss'] = 0.2

    # Add risk management parameters
    enhanced_strategy['position_size'] = 1  # Number of lots
    enhanced_strategy['max_risk_per_trade'] = 0.02  # 2% of portfolio

    return enhanced_strategy
